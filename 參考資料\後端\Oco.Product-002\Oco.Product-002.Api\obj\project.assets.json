{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["Cronos >= 0.8.4", "DateOnlyTimeOnly.AspNet.Swashbuckle >= 2.1.1", "FluentValidation >= 11.9.1", "FluentValidation.DependencyInjectionExtensions >= 11.9.1", "Microsoft.EntityFrameworkCore.SqlServer >= 8.0.5", "Microsoft.EntityFrameworkCore.Tools >= 8.0.5", "Microsoft.Extensions.Hosting.WindowsServices >= 8.0.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.20.1", "Newtonsoft.Json >= 13.0.3", "Oco.Core.Modbus >= 0.9.2", "Oco.Desigocc.Share >= 1.0.4", "RestSharp >= 112.0.0", "Scrutor >= 4.2.2", "Serilog.AspNetCore >= 8.0.1", "Swashbuckle.AspNetCore >= 6.6.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj", "projectName": "Oco.Product-002.Api", "projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Cronos": {"target": "Package", "version": "[0.8.4, )"}, "DateOnlyTimeOnly.AspNet.Swashbuckle": {"target": "Package", "version": "[2.1.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.20.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Oco.Core.Modbus": {"target": "Package", "version": "[0.9.2, )"}, "Oco.Desigocc.Share": {"target": "Package", "version": "[1.0.4, )"}, "RestSharp": {"target": "Package", "version": "[112.0.0, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1803", "level": "Warning", "warningLevel": 1, "message": "You are running the 'restore' operation with an 'HTTP' source, 'http://192.168.1.154:14235/v3/index.json'. Non-HTTPS access will be removed in a future version. Consider migrating to an 'HTTPS' source."}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Oco.Core.Modbus"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.EntityFrameworkCore.SqlServer"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.EntityFrameworkCore.Tools"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "<PERSON><PERSON><PERSON>"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Cronos"}]}