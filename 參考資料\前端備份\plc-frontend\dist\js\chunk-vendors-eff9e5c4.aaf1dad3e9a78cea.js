"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[4756],{560:function(e,n,t){t.d(n,{SC:function(){return i},Uz:function(){return r},_3:function(){return d},sm:function(){return c}});var a=t(20641),o=t(79841),l=Symbol("TreeContextKey"),r=(0,a.pM)({compatConfig:{MODE:3},name:"TreeContext",props:{value:{type:Object}},setup:function(e,n){var t=n.slots;return(0,a.Gt)(l,(0,a.EW)((function(){return e.value}))),function(){var e;return null===(e=t.default)||void 0===e?void 0:e.call(t)}}}),i=function(){return(0,a.WQ)(l,(0,a.EW)((function(){return{}})))},u=Symbol("KeysStateKey"),d=function(e){(0,a.Gt)(u,e)},c=function(){return(0,a.WQ)(u,{expandedKeys:(0,o.IJ)([]),selectedKeys:(0,o.IJ)([]),loadedKeys:(0,o.IJ)([]),loadingKeys:(0,o.IJ)([]),checkedKeys:(0,o.IJ)([]),halfCheckedKeys:(0,o.IJ)([]),expandedKeysSet:(0,a.EW)((function(){return new Set})),selectedKeysSet:(0,a.EW)((function(){return new Set})),loadedKeysSet:(0,a.EW)((function(){return new Set})),loadingKeysSet:(0,a.EW)((function(){return new Set})),checkedKeysSet:(0,a.EW)((function(){return new Set})),halfCheckedKeysSet:(0,a.EW)((function(){return new Set})),flattenNodes:(0,o.IJ)([])})}},24274:function(e,n,t){t.d(n,{A:function(){return m}});var a=t(73354),o=t(94494),l=t(22855),r=t(88428),i=t(20641),u=t(560),d=function(e){for(var n=e.prefixCls,t=e.level,o=e.isStart,l=e.isEnd,r="".concat(n,"-indent-unit"),u=[],d=0;d<t;d+=1){var c;u.push((0,i.bF)("span",{key:d,class:(c={},(0,a.A)(c,r,!0),(0,a.A)(c,"".concat(r,"-start"),o[d]),(0,a.A)(c,"".concat(r,"-end"),l[d]),c)},null))}return(0,i.bF)("span",{"aria-hidden":"true",class:"".concat(n,"-indent")},[u])},c=d,v=t(50214),s=t(79841),f=t(47036),p=t(58777),y=t(57646),h=t(11926),g=t(53770),K=["eventKey","isLeaf","isStart","isEnd","domRef","active","data","onMousemove","selectable"],b="open",k="close",A="---",m=(0,i.pM)({compatConfig:{MODE:3},name:"ATreeNode",inheritAttrs:!1,props:f.Tf,isTreeNode:1,slots:["title","icon","switcherIcon"],setup:function(e,n){var t=n.attrs,d=n.slots,f=n.expose;(0,y.$e)(!("slots"in e.data),"treeData slots is deprecated, please use ".concat(Object.keys(e.data.slots||{}).map((function(e){return"`v-slot:"+e+"` "})),"instead"));var m=(0,s.KR)(!1),C=(0,u.SC)(),E=(0,u.sm)(),x=E.expandedKeysSet,N=E.selectedKeysSet,S=E.loadedKeysSet,w=E.loadingKeysSet,D=E.checkedKeysSet,F=E.halfCheckedKeysSet,O=C.value,T=O.dragOverNodeKey,P=O.dropPosition,B=O.keyEntities,I=(0,i.EW)((function(){return(0,v.N5)(e.eventKey,{expandedKeysSet:x.value,selectedKeysSet:N.value,loadedKeysSet:S.value,loadingKeysSet:w.value,checkedKeysSet:D.value,halfCheckedKeysSet:F.value,dragOverNodeKey:T,dropPosition:P,keyEntities:B})})),L=(0,g.A)((function(){return I.value.expanded})),W=(0,g.A)((function(){return I.value.selected})),M=(0,g.A)((function(){return I.value.checked})),R=(0,g.A)((function(){return I.value.loaded})),j=(0,g.A)((function(){return I.value.loading})),J=(0,g.A)((function(){return I.value.halfChecked})),H=(0,g.A)((function(){return I.value.dragOver})),$=(0,g.A)((function(){return I.value.dragOverGapTop})),_=(0,g.A)((function(){return I.value.dragOverGapBottom})),G=(0,g.A)((function(){return I.value.pos})),z=(0,s.KR)(),U=(0,i.EW)((function(){var n=e.eventKey,t=C.value.keyEntities,a=t[n]||{},o=a.children;return!!(o||[]).length})),V=(0,i.EW)((function(){var n=e.isLeaf,t=C.value.loadData,a=U.value;return!1!==n&&(n||!t&&!a||t&&R.value&&!a)})),Y=(0,i.EW)((function(){return V.value?null:L.value?b:k})),Q=(0,i.EW)((function(){var n=e.disabled,t=C.value.disabled;return!(!t&&!n)})),X=(0,i.EW)((function(){var n=e.checkable,t=C.value.checkable;return!(!t||!1===n)&&t})),Z=(0,i.EW)((function(){var n=e.selectable,t=C.value.selectable;return"boolean"===typeof n?n:t})),q=(0,i.EW)((function(){var n=e.data,t=e.active,a=e.checkable,o=e.disableCheckbox,l=e.disabled,i=e.selectable;return(0,r.A)((0,r.A)({active:t,checkable:a,disableCheckbox:o,disabled:l,selectable:i},n),{},{dataRef:n,data:n,isLeaf:V.value,checked:M.value,expanded:L.value,loading:j.value,selected:W.value,halfChecked:J.value})})),ee=(0,i.nI)(),ne=(0,i.EW)((function(){var n=e.eventKey,t=C.value.keyEntities,a=t[n]||{},o=a.parent;return(0,r.A)((0,r.A)({},(0,v.Hj)((0,l.A)({},e,I.value))),{},{parent:o})})),te=(0,s.Kh)({eventData:ne,eventKey:(0,i.EW)((function(){return e.eventKey})),selectHandle:z,pos:G,key:ee.vnode.key});f(te);var ae=function(e){var n=C.value.onNodeDoubleClick;n(e,ne.value)},oe=function(e){if(!Q.value){var n=C.value.onNodeSelect;e.preventDefault(),n(e,ne.value)}},le=function(n){if(!Q.value){var t=e.disableCheckbox,a=C.value.onNodeCheck;if(X.value&&!t){n.preventDefault();var o=!M.value;a(n,ne.value,o)}}},re=function(e){var n=C.value.onNodeClick;n(e,ne.value),Z.value?oe(e):le(e)},ie=function(e){var n=C.value.onNodeMouseEnter;n(e,ne.value)},ue=function(e){var n=C.value.onNodeMouseLeave;n(e,ne.value)},de=function(e){var n=C.value.onNodeContextMenu;n(e,ne.value)},ce=function(e){var n=C.value.onNodeDragStart;e.stopPropagation(),m.value=!0,n(e,te);try{e.dataTransfer.setData("text/plain","")}catch(t){}},ve=function(e){var n=C.value.onNodeDragEnter;e.preventDefault(),e.stopPropagation(),n(e,te)},se=function(e){var n=C.value.onNodeDragOver;e.preventDefault(),e.stopPropagation(),n(e,te)},fe=function(e){var n=C.value.onNodeDragLeave;e.stopPropagation(),n(e,te)},pe=function(e){var n=C.value.onNodeDragEnd;e.stopPropagation(),m.value=!1,n(e,te)},ye=function(e){var n=C.value.onNodeDrop;e.preventDefault(),e.stopPropagation(),m.value=!1,n(e,te)},he=function(e){var n=C.value.onNodeExpand;j.value||n(e,ne.value)},ge=function(){var n=e.data,t=C.value.draggable;return!(!t||t.nodeDraggable&&!t.nodeDraggable(n))},Ke=function(){var e=C.value,n=e.draggable,t=e.prefixCls;return n&&null!==n&&void 0!==n&&n.icon?(0,i.bF)("span",{class:"".concat(t,"-draggable-icon")},[n.icon]):null},be=function(){var n,t,a,o=e.switcherIcon,l=void 0===o?d.switcherIcon||(null===(n=C.value.slots)||void 0===n?void 0:n[null===(t=e.data)||void 0===t||null===(a=t.slots)||void 0===a?void 0:a.switcherIcon]):o,r=C.value.switcherIcon,i=l||r;return"function"===typeof i?i(q.value):i},ke=function(){var e=C.value,n=e.loadData,t=e.onNodeLoad;j.value||n&&L.value&&!V.value&&(U.value||R.value||t(ne.value))};(0,i.sV)((function(){ke()})),(0,i.$u)((function(){ke()}));var Ae=function(){var e=C.value.prefixCls,n=be();if(V.value)return!1!==n?(0,i.bF)("span",{class:(0,p.A)("".concat(e,"-switcher"),"".concat(e,"-switcher-noop"))},[n]):null;var t=(0,p.A)("".concat(e,"-switcher"),"".concat(e,"-switcher_").concat(L.value?b:k));return!1!==n?(0,i.bF)("span",{onClick:he,class:t},[n]):null},me=function(){var n,t,a=e.disableCheckbox,o=C.value.prefixCls,l=Q.value,r=X.value;return r?(0,i.bF)("span",{class:(0,p.A)("".concat(o,"-checkbox"),M.value&&"".concat(o,"-checkbox-checked"),!M.value&&J.value&&"".concat(o,"-checkbox-indeterminate"),(l||a)&&"".concat(o,"-checkbox-disabled")),onClick:le},[null===(n=(t=C.value).customCheckable)||void 0===n?void 0:n.call(t)]):null},Ce=function(){var e=C.value.prefixCls;return(0,i.bF)("span",{class:(0,p.A)("".concat(e,"-iconEle"),"".concat(e,"-icon__").concat(Y.value||"docu"),j.value&&"".concat(e,"-icon_loading"))},null)},Ee=function(){var n=e.disabled,t=e.eventKey,a=C.value,o=a.draggable,l=a.dropLevelOffset,r=a.dropPosition,i=a.prefixCls,u=a.indent,d=a.dropIndicatorRender,c=a.dragOverNodeKey,v=a.direction,s=!1!==o,f=!n&&s&&c===t;return f?d({dropPosition:r,dropLevelOffset:l,indent:u,prefixCls:i,direction:v}):null},xe=function(){var n,t,a,o,l,r,u=e.icon,c=void 0===u?d.icon:u,v=e.data,s=d.title||(null===(n=C.value.slots)||void 0===n?void 0:n[null===(t=e.data)||void 0===t||null===(a=t.slots)||void 0===a?void 0:a.title])||(null===(o=C.value.slots)||void 0===o?void 0:o.title)||e.title,f=C.value,y=f.prefixCls,h=f.showIcon,g=f.icon,K=f.loadData,b=Q.value,k="".concat(y,"-node-content-wrapper");if(h){var E,x,N=c||(null===(E=C.value.slots)||void 0===E?void 0:E[null===v||void 0===v||null===(x=v.slots)||void 0===x?void 0:x.icon])||g;l=N?(0,i.bF)("span",{class:(0,p.A)("".concat(y,"-iconEle"),"".concat(y,"-icon__customize"))},["function"===typeof N?N(q.value):N]):Ce()}else K&&j.value&&(l=Ce());r="function"===typeof s?s(q.value):s,r=void 0===r?A:r;var S=(0,i.bF)("span",{class:"".concat(y,"-title")},[r]);return(0,i.bF)("span",{ref:z,title:"string"===typeof s?s:"",class:(0,p.A)("".concat(k),"".concat(k,"-").concat(Y.value||"normal"),!b&&(W.value||m.value)&&"".concat(y,"-node-selected")),onMouseenter:ie,onMouseleave:ue,onContextmenu:de,onClick:re,onDblclick:ae},[l,S,Ee()])};return function(){var n,l=(0,r.A)((0,r.A)({},e),t),u=l.eventKey,d=l.isLeaf,v=l.isStart,s=l.isEnd,f=l.domRef,y=l.active,g=(l.data,l.onMousemove),b=l.selectable,k=(0,o.A)(l,K),A=C.value,m=A.prefixCls,E=A.filterTreeNode,x=A.keyEntities,N=A.dropContainerKey,S=A.dropTargetKey,w=A.draggingNodeKey,D=Q.value,F=(0,h.A)(k,{aria:!0,data:!0}),O=x[u]||{},T=O.level,P=s[s.length-1],B=ge(),I=!D&&B,R=w===u,G=void 0!==b?{"aria-selected":!!b}:void 0;return(0,i.bF)("div",(0,r.A)((0,r.A)({ref:f,class:(0,p.A)(t.class,"".concat(m,"-treenode"),(n={},(0,a.A)(n,"".concat(m,"-treenode-disabled"),D),(0,a.A)(n,"".concat(m,"-treenode-switcher-").concat(L.value?"open":"close"),!d),(0,a.A)(n,"".concat(m,"-treenode-checkbox-checked"),M.value),(0,a.A)(n,"".concat(m,"-treenode-checkbox-indeterminate"),J.value),(0,a.A)(n,"".concat(m,"-treenode-selected"),W.value),(0,a.A)(n,"".concat(m,"-treenode-loading"),j.value),(0,a.A)(n,"".concat(m,"-treenode-active"),y),(0,a.A)(n,"".concat(m,"-treenode-leaf-last"),P),(0,a.A)(n,"".concat(m,"-treenode-draggable"),I),(0,a.A)(n,"dragging",R),(0,a.A)(n,"drop-target",S===u),(0,a.A)(n,"drop-container",N===u),(0,a.A)(n,"drag-over",!D&&H.value),(0,a.A)(n,"drag-over-gap-top",!D&&$.value),(0,a.A)(n,"drag-over-gap-bottom",!D&&_.value),(0,a.A)(n,"filter-node",E&&E(ne.value)),n)),style:t.style,draggable:I,"aria-grabbed":R,onDragstart:I?ce:void 0,onDragenter:B?ve:void 0,onDragover:B?se:void 0,onDragleave:B?fe:void 0,onDrop:B?ye:void 0,onDragend:B?pe:void 0,onMousemove:g},G),F),[(0,i.bF)(c,{prefixCls:m,level:T,isStart:v,isEnd:s},null),Ke(),Ae(),me(),xe()])}}})},47036:function(e,n,t){t.d(n,{DP:function(){return r},Tf:function(){return o},zb:function(){return l}});var a=t(4718),o={eventKey:[String,Number],prefixCls:String,title:a.A.any,data:{type:Object,default:void 0},parent:{type:Object,default:void 0},isStart:{type:Array},isEnd:{type:Array},active:{type:Boolean,default:void 0},onMousemove:{type:Function},isLeaf:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},selectable:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},disableCheckbox:{type:Boolean,default:void 0},icon:a.A.any,switcherIcon:a.A.any,domRef:{type:Function}},l={prefixCls:{type:String},motion:{type:Object},focusable:{type:Boolean},activeItem:{type:Object},focused:{type:Boolean},tabindex:{type:Number},checkable:{type:Boolean},selectable:{type:Boolean},disabled:{type:Boolean},height:{type:Number},itemHeight:{type:Number},virtual:{type:Boolean},onScroll:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onActiveChange:{type:Function},onContextmenu:{type:Function},onListChangeStart:{type:Function},onListChangeEnd:{type:Function}},r=function(){return{prefixCls:String,focusable:{type:Boolean,default:void 0},activeKey:[Number,String],tabindex:Number,children:a.A.any,treeData:{type:Array},fieldNames:{type:Object},showLine:{type:[Boolean,Object],default:void 0},showIcon:{type:Boolean,default:void 0},icon:a.A.any,selectable:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},multiple:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},checkStrictly:{type:Boolean,default:void 0},draggable:{type:[Function,Boolean]},defaultExpandParent:{type:Boolean,default:void 0},autoExpandParent:{type:Boolean,default:void 0},defaultExpandAll:{type:Boolean,default:void 0},defaultExpandedKeys:{type:Array},expandedKeys:{type:Array},defaultCheckedKeys:{type:Array},checkedKeys:{type:[Object,Array]},defaultSelectedKeys:{type:Array},selectedKeys:{type:Array},allowDrop:{type:Function},dropIndicatorRender:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onKeydown:{type:Function},onContextmenu:{type:Function},onClick:{type:Function},onDblclick:{type:Function},onScroll:{type:Function},onExpand:{type:Function},onCheck:{type:Function},onSelect:{type:Function},onLoad:{type:Function},loadData:{type:Function},loadedKeys:{type:Array},onMouseenter:{type:Function},onMouseleave:{type:Function},onRightClick:{type:Function},onDragstart:{type:Function},onDragenter:{type:Function},onDragover:{type:Function},onDragleave:{type:Function},onDragend:{type:Function},onDrop:{type:Function},onActiveChange:{type:Function},filterTreeNode:{type:Function},motion:a.A.any,switcherIcon:a.A.any,height:Number,itemHeight:Number,virtual:{type:Boolean,default:void 0},direction:{type:String}}}},56794:function(e,n,t){var a=t(57329);n.A=a.A},57329:function(e,n,t){t.d(n,{A:function(){return G}});var a=t(2921),o=t(55794),l=t(22855),r=t(14517),i=t(73354),u=t(88428),d=t(20641),c=t(560),v=t(55780),s=t(50214),f=t(57343),p=t(94494),y=t(79841),h=t(45561),g=t(11712),K=t(9322),b=t(24274),k=t(47036),A=t(50380),m=["motion","motionNodes","motionType","active","eventKey"],C=(0,d.pM)({compatConfig:{MODE:3},name:"MotionTreeNode",inheritAttrs:!1,props:(0,u.A)((0,u.A)({},k.Tf),{},{active:Boolean,motion:Object,motionNodes:{type:Array},onMotionStart:Function,onMotionEnd:Function,motionType:String}),slots:["title","icon","switcherIcon","checkable"],setup:function(e,n){var t=n.attrs,a=n.slots,o=(0,y.KR)(!0),r=(0,c.SC)(),i=(0,y.KR)(!1),v=(0,d.EW)((function(){return e.motion?e.motion:(0,A.A)()})),s=function(n,t){var a,o;if("appear"===t)null===(a=v.value)||void 0===a||null===(o=a.onAfterEnter)||void 0===o||o.call(a,n);else if("leave"===t){var l,r;null===(l=v.value)||void 0===l||null===(r=l.onAfterLeave)||void 0===r||r.call(l,n)}i.value||e.onMotionEnd(),i.value=!0};return(0,d.wB)((function(){return e.motionNodes}),(function(){e.motionNodes&&"hide"===e.motionType&&o.value&&(0,d.dY)((function(){o.value=!1}))}),{immediate:!0,flush:"post"}),(0,d.sV)((function(){e.motionNodes&&e.onMotionStart()})),(0,d.xo)((function(){e.motionNodes&&s()})),function(){e.motion;var n=e.motionNodes,i=e.motionType,c=e.active,h=e.eventKey,g=(0,p.A)(e,m);return n?(0,d.bF)(K.eB,(0,u.A)((0,u.A)({},v.value),{},{appear:"show"===i,onAfterAppear:function(e){return s(e,"appear")},onAfterLeave:function(e){return s(e,"leave")}}),{default:function(){return[(0,d.bo)((0,d.bF)("div",{class:"".concat(r.value.prefixCls,"-treenode-motion")},[n.map((function(e){var n=(0,l.A)({},((0,f.A)(e.data),e.data)),t=e.title,o=e.key,r=e.isStart,i=e.isEnd;return delete n.children,(0,d.bF)(b.A,(0,u.A)((0,u.A)({},n),{},{title:t,active:c,data:e.data,key:o,eventKey:o,isStart:r,isEnd:i}),a)}))]),[[K.aG,o.value]])]}}):(0,d.bF)(b.A,(0,u.A)((0,u.A)({domRef:y.KR,class:t.class,style:t.style},g),{},{active:c,eventKey:h}),a)}}}),E=t(76205),x=["prefixCls","selectable","checkable","disabled","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabindex","onKeydown","onFocus","onBlur","onListChangeStart","onListChangeEnd"],N={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},S=function(){},w="RC_TREE_MOTION_".concat(Math.random()),D={key:w},F={key:w,level:0,index:0,pos:"0",node:D,nodes:[D]},O={parent:null,children:[],pos:F.pos,data:D,title:null,key:w,isStart:[],isEnd:[]};function T(e,n,t,a){return!1!==n&&t?e.slice(0,Math.ceil(t/a)+1):e}function P(e){var n=e.key,t=e.pos;return(0,s.i7)(n,t)}function B(e){var n=String(e.key),t=e;while(t.parent)t=t.parent,n="".concat(t.key," > ").concat(n);return n}var I=(0,d.pM)({compatConfig:{MODE:3},name:"NodeList",inheritAttrs:!1,props:k.zb,setup:function(e,n){var t=n.expose,a=n.attrs,o=(0,y.KR)(),i=(0,y.KR)(),v=(0,c.sm)(),K=v.expandedKeys,b=v.flattenNodes;t({scrollTo:function(e){o.value.scrollTo(e)},getIndentWidth:function(){return i.value.offsetWidth}});var k=(0,y.IJ)(b.value),A=(0,y.IJ)([]),m=(0,y.KR)(null);function D(){k.value=b.value,A.value=[],m.value=null,e.onListChangeEnd()}var F=(0,c.SC)();(0,d.wB)([function(){return K.value.slice()},b],(function(n,t){var a=(0,r.A)(n,2),o=a[0],l=a[1],i=(0,r.A)(t,2),u=i[0],d=i[1],c=(0,E.j)(u,o);if(null!==c.key){var v=e.virtual,s=e.height,f=e.itemHeight;if(c.add){var p=d.findIndex((function(e){var n=e.key;return n===c.key})),y=T((0,E.u)(d,l,c.key),v,s,f),h=d.slice();h.splice(p+1,0,O),k.value=h,A.value=y,m.value="show"}else{var g=l.findIndex((function(e){var n=e.key;return n===c.key})),K=T((0,E.u)(l,d,c.key),v,s,f),b=l.slice();b.splice(g+1,0,O),k.value=b,A.value=K,m.value="hide"}}else d!==l&&(k.value=l)})),(0,d.wB)((function(){return F.value.dragging}),(function(e){e||D()}));var I=(0,d.EW)((function(){return void 0===e.motion?k.value:b.value})),L=function(){e.onActiveChange(null)};return function(){var n=(0,u.A)((0,u.A)({},e),a),t=n.prefixCls,r=(n.selectable,n.checkable,n.disabled),c=n.motion,v=n.height,y=n.itemHeight,K=n.virtual,b=n.focusable,k=n.activeItem,E=n.focused,F=n.tabindex,O=n.onKeydown,T=n.onFocus,W=n.onBlur,M=n.onListChangeStart,R=(n.onListChangeEnd,(0,p.A)(n,x));return(0,d.bF)(d.FK,null,[E&&k&&(0,d.bF)("span",{style:N,"aria-live":"assertive"},[B(k)]),(0,d.bF)("div",null,[(0,d.bF)("input",{style:N,disabled:!1===b||r,tabindex:!1!==b?F:null,onKeydown:O,onFocus:T,onBlur:W,value:"",onChange:S,"aria-label":"for screen reader"},null)]),(0,d.bF)("div",{class:"".concat(t,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden"}},[(0,d.bF)("div",{class:"".concat(t,"-indent")},[(0,d.bF)("div",{ref:i,class:"".concat(t,"-indent-unit")},null)])]),(0,d.bF)(h.A,(0,u.A)((0,u.A)({},(0,g.A)(R,["onActiveChange"])),{},{data:I.value,itemKey:P,height:v,fullHeight:!1,virtual:K,itemHeight:y,prefixCls:"".concat(t,"-list"),ref:o,onVisibleChange:function(e,n){var t=new Set(e),a=n.filter((function(e){return!t.has(e)}));a.some((function(e){return P(e)===w}))&&D()}}),{default:function(e){var n=e.pos,t=(0,l.A)({},((0,f.A)(e.data),e.data)),a=e.title,o=e.key,r=e.isStart,i=e.isEnd,v=(0,s.i7)(o,n);return delete t.key,delete t.children,(0,d.bF)(C,(0,u.A)((0,u.A)({},t),{},{eventKey:v,title:a,active:!!k&&o===k.key,data:e.data,isStart:r,isEnd:i,motion:c,motionNodes:o===w?A.value:null,motionType:m.value,onMotionStart:M,onMotionEnd:D,onMousemove:L}),null)}})])}}}),L=t(5860);function W(e){var n=e.dropPosition,t=e.dropLevelOffset,a=e.indent,o={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:"".concat(2,"px")};switch(n){case-1:o.top=0,o.left="".concat(-t*a,"px");break;case 1:o.bottom=0,o.left="".concat(-t*a,"px");break;case 0:o.bottom=0,o.left="".concat(a);break}return(0,d.bF)("div",{style:o},null)}var M=t(51636),R=t(57646),j=t(11207),J=t(58777),H=t(11926),$=t(30573),_=10,G=(0,d.pM)({compatConfig:{MODE:3},name:"Tree",inheritAttrs:!1,slots:["checkable","title","icon","titleRender"],props:(0,M.A)((0,k.DP)(),{prefixCls:"vc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:W,allowDrop:function(){return!0}}),setup:function(e,n){var t=n.attrs,f=n.slots,p=n.expose,h=(0,y.KR)(!1),g={},K=(0,y.KR)(),b=(0,y.IJ)([]),k=(0,y.IJ)([]),A=(0,y.IJ)([]),m=(0,y.IJ)([]),C=(0,y.IJ)([]),E=(0,y.IJ)([]),x={},N=(0,y.Kh)({draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null}),S=(0,y.IJ)([]);(0,d.wB)([function(){return e.treeData},function(){return e.children}],(function(){S.value=void 0!==e.treeData?(0,y.ux)(e.treeData).slice():(0,s.vH)((0,y.ux)(e.children))}),{immediate:!0,deep:!0});var D=(0,y.IJ)({}),O=(0,y.KR)(!1),T=(0,y.KR)(null),P=(0,y.KR)(!1),B=(0,d.EW)((function(){return(0,s.AZ)(e.fieldNames)})),W=(0,y.KR)(),M=null,G=null,z=null,U=(0,d.EW)((function(){return{expandedKeysSet:V.value,selectedKeysSet:Y.value,loadedKeysSet:Q.value,loadingKeysSet:X.value,checkedKeysSet:Z.value,halfCheckedKeysSet:q.value,dragOverNodeKey:N.dragOverNodeKey,dropPosition:N.dropPosition,keyEntities:D.value}})),V=(0,d.EW)((function(){return new Set(E.value)})),Y=(0,d.EW)((function(){return new Set(b.value)})),Q=(0,d.EW)((function(){return new Set(m.value)})),X=(0,d.EW)((function(){return new Set(C.value)})),Z=(0,d.EW)((function(){return new Set(k.value)})),q=(0,d.EW)((function(){return new Set(A.value)}));(0,d.nT)((function(){if(S.value){var e=(0,s.cG)(S.value,{fieldNames:B.value});D.value=(0,u.A)((0,i.A)({},w,F),e.keyEntities)}}));var ee=!1;(0,d.wB)([function(){return e.expandedKeys},function(){return e.autoExpandParent},D],(function(n,t){var a=(0,r.A)(n,2),o=(a[0],a[1]),l=(0,r.A)(t,2),i=(l[0],l[1]),d=E.value;if(void 0!==e.expandedKeys||ee&&o!==i)d=e.autoExpandParent||!ee&&e.defaultExpandParent?(0,v.hr)(e.expandedKeys,D.value):e.expandedKeys;else if(!ee&&e.defaultExpandAll){var c=(0,u.A)({},D.value);delete c[w],d=Object.keys(c).map((function(e){return c[e].key}))}else!ee&&e.defaultExpandedKeys&&(d=e.autoExpandParent||e.defaultExpandParent?(0,v.hr)(e.defaultExpandedKeys,D.value):e.defaultExpandedKeys);d&&(E.value=d),ee=!0}),{immediate:!0});var ne=(0,y.IJ)([]);(0,d.nT)((function(){ne.value=(0,s.$9)(S.value,E.value,B.value)})),(0,d.nT)((function(){e.selectable&&(void 0!==e.selectedKeys?b.value=(0,v.BE)(e.selectedKeys,e):!ee&&e.defaultSelectedKeys&&(b.value=(0,v.BE)(e.defaultSelectedKeys,e)))}));var te=(0,$.A)(D),ae=te.maxLevel,oe=te.levelEntities;(0,d.nT)((function(){var n;if(e.checkable&&(void 0!==e.checkedKeys?n=(0,v.tg)(e.checkedKeys)||{}:!ee&&e.defaultCheckedKeys?n=(0,v.tg)(e.defaultCheckedKeys)||{}:S.value&&(n=(0,v.tg)(e.checkedKeys)||{checkedKeys:k.value,halfCheckedKeys:A.value}),n)){var t=n,a=t.checkedKeys,o=void 0===a?[]:a,l=t.halfCheckedKeys,r=void 0===l?[]:l;if(!e.checkStrictly){var i=(0,L.p)(o,!0,D.value,ae.value,oe.value);o=i.checkedKeys,r=i.halfCheckedKeys}k.value=o,A.value=r}})),(0,d.nT)((function(){e.loadedKeys&&(m.value=e.loadedKeys)}));var le=function(){(0,l.A)(N,{dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})},re=function(e){W.value.scrollTo(e)};(0,d.wB)((function(){return e.activeKey}),(function(){void 0!==e.activeKey&&(T.value=e.activeKey)}),{immediate:!0}),(0,d.wB)(T,(function(e){(0,d.dY)((function(){null!==e&&re({key:e})}))}),{immediate:!0,flush:"post"});var ie=function(n){void 0===e.expandedKeys&&(E.value=n)},ue=function(){null!==N.draggingNodeKey&&(0,l.A)(N,{draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),M=null,z=null},de=function(n,t){var a=e.onDragend;N.dragOverNodeKey=null,ue(),null===a||void 0===a||a({event:n,node:t.eventData}),G=null},ce=function e(n){de(n,null,!0),window.removeEventListener("dragend",e)},ve=function(n,t){var a=e.onDragstart,o=t.eventKey,l=t.eventData;G=t,M={x:n.clientX,y:n.clientY};var r=(0,v.BA)(E.value,o);N.draggingNodeKey=o,N.dragChildrenKeys=(0,v.kG)(o,D.value),K.value=W.value.getIndentWidth(),ie(r),window.addEventListener("dragend",ce),a&&a({event:n,node:l})},se=function(n,t){var a=e.onDragenter,o=e.onExpand,r=e.allowDrop,i=e.direction,u=t.pos,d=t.eventKey;if(z!==d&&(z=d),G){var c=(0,v.Oh)(n,G,t,K.value,M,r,ne.value,D.value,V.value,i),s=c.dropPosition,f=c.dropLevelOffset,p=c.dropTargetKey,y=c.dropContainerKey,h=c.dropTargetPos,b=c.dropAllowed,k=c.dragOverNodeKey;-1===N.dragChildrenKeys.indexOf(p)&&b?(g||(g={}),Object.keys(g).forEach((function(e){clearTimeout(g[e])})),G.eventKey!==t.eventKey&&(g[u]=window.setTimeout((function(){if(null!==N.draggingNodeKey){var e=E.value.slice(),a=D.value[t.eventKey];a&&(a.children||[]).length&&(e=(0,v.$s)(E.value,t.eventKey)),ie(e),o&&o(e,{node:t.eventData,expanded:!0,nativeEvent:n})}}),800)),G.eventKey!==p||0!==f?((0,l.A)(N,{dragOverNodeKey:k,dropPosition:s,dropLevelOffset:f,dropTargetKey:p,dropContainerKey:y,dropTargetPos:h,dropAllowed:b}),a&&a({event:n,node:t.eventData,expandedKeys:E.value})):le()):le()}else le()},fe=function(n,t){var a=e.onDragover,o=e.allowDrop,r=e.direction;if(G){var i=(0,v.Oh)(n,G,t,K.value,M,o,ne.value,D.value,V.value,r),u=i.dropPosition,d=i.dropLevelOffset,c=i.dropTargetKey,s=i.dropContainerKey,f=i.dropAllowed,p=i.dropTargetPos,y=i.dragOverNodeKey;-1===N.dragChildrenKeys.indexOf(c)&&f&&(G.eventKey===c&&0===d?null===N.dropPosition&&null===N.dropLevelOffset&&null===N.dropTargetKey&&null===N.dropContainerKey&&null===N.dropTargetPos&&!1===N.dropAllowed&&null===N.dragOverNodeKey||le():u===N.dropPosition&&d===N.dropLevelOffset&&c===N.dropTargetKey&&s===N.dropContainerKey&&p===N.dropTargetPos&&f===N.dropAllowed&&y===N.dragOverNodeKey||(0,l.A)(N,{dropPosition:u,dropLevelOffset:d,dropTargetKey:c,dropContainerKey:s,dropTargetPos:p,dropAllowed:f,dragOverNodeKey:y}),a&&a({event:n,node:t.eventData}))}},pe=function(n,t){z!==t.eventKey||n.currentTarget.contains(n.relatedTarget)||(le(),z=null);var a=e.onDragleave;a&&a({event:n,node:t.eventData})},ye=function(n,t){var a,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=N.dragChildrenKeys,r=N.dropPosition,i=N.dropTargetKey,d=N.dropTargetPos,c=N.dropAllowed;if(c){var f=e.onDrop;if(N.dragOverNodeKey=null,ue(),null!==i){var p=(0,u.A)((0,u.A)({},(0,s.N5)(i,(0,y.ux)(U.value))),{},{active:(null===(a=Fe.value)||void 0===a?void 0:a.key)===i,data:D.value[i].node}),h=-1!==l.indexOf(i);(0,R.$e)(!h,"Can not drop to dragNode's children node. Maybe this is a bug of ant-design-vue. Please report an issue.");var g=(0,v.LI)(d),K={event:n,node:(0,s.Hj)(p),dragNode:G?G.eventData:null,dragNodesKeys:[G.eventKey].concat(l),dropToGap:0!==r,dropPosition:r+Number(g[g.length-1])};o||null===f||void 0===f||f(K),G=null}}},he=function(n,t){var a=e.onClick;a&&a(n,t)},ge=function(n,t){var a=e.onDblclick;a&&a(n,t)},Ke=function(n,t){var a=b.value,o=e.onSelect,l=e.multiple,r=t.selected,i=t[B.value.key],u=!r;a=u?l?(0,v.$s)(a,i):[i]:(0,v.BA)(a,i);var d=D.value,c=a.map((function(e){var n=d[e];return n?n.node:null})).filter((function(e){return e}));void 0===e.selectedKeys&&(b.value=a),o&&o(a,{event:"select",selected:u,node:t,selectedNodes:c,nativeEvent:n})},be=function(n,t,a){var l,r=e.checkStrictly,i=e.onCheck,u=t[B.value.key],d={event:"check",node:t,checked:a,nativeEvent:n},c=D.value;if(r){var s=a?(0,v.$s)(k.value,u):(0,v.BA)(k.value,u),f=(0,v.BA)(A.value,u);l={checked:s,halfChecked:f},d.checkedNodes=s.map((function(e){return c[e]})).filter((function(e){return e})).map((function(e){return e.node})),void 0===e.checkedKeys&&(k.value=s)}else{var p=(0,L.p)([].concat((0,o.A)(k.value),[u]),!0,c,ae.value,oe.value),y=p.checkedKeys,h=p.halfCheckedKeys;if(!a){var g=new Set(y);g.delete(u);var K=(0,L.p)(Array.from(g),{checked:!1,halfCheckedKeys:h},c,ae.value,oe.value);y=K.checkedKeys,h=K.halfCheckedKeys}l=y,d.checkedNodes=[],d.checkedNodesPositions=[],d.halfCheckedKeys=h,y.forEach((function(e){var n=c[e];if(n){var t=n.node,a=n.pos;d.checkedNodes.push(t),d.checkedNodesPositions.push({node:t,pos:a})}})),void 0===e.checkedKeys&&(k.value=y,A.value=h)}i&&i(l,d)},ke=function(n){var t=n[B.value.key],a=new Promise((function(a,o){var l=e.loadData,r=e.onLoad;if(!l||Q.value.has(t)||X.value.has(t))return null;var i=l(n);i.then((function(){var o=(0,v.$s)(m.value,t),l=(0,v.BA)(C.value,t);r&&r(o,{event:"load",node:n}),void 0===e.loadedKeys&&(m.value=o),C.value=l,a()})).catch((function(n){var l=(0,v.BA)(C.value,t);if(C.value=l,x[t]=(x[t]||0)+1,x[t]>=_){(0,R.$e)(!1,"Retry for `loadData` many times but still failed. No more retry.");var r=(0,v.$s)(m.value,t);void 0===e.loadedKeys&&(m.value=r),a()}o(n)})),C.value=(0,v.$s)(C.value,t)}));return a.catch((function(){})),a},Ae=function(n,t){var a=e.onMouseenter;a&&a({event:n,node:t})},me=function(n,t){var a=e.onMouseleave;a&&a({event:n,node:t})},Ce=function(n,t){var a=e.onRightClick;a&&(n.preventDefault(),a({event:n,node:t}))},Ee=function(n){var t=e.onFocus;O.value=!0,t&&t(n)},xe=function(n){var t=e.onBlur;O.value=!1,De(null),t&&t(n)},Ne=function(n,t){var a=E.value,o=e.onExpand,l=e.loadData,r=t.expanded,i=t[B.value.key];if(!P.value){var u=a.indexOf(i),d=!r;if((0,R.$e)(r&&-1!==u||!r&&-1===u,"Expand state not sync with index check"),a=d?(0,v.$s)(a,i):(0,v.BA)(a,i),ie(a),o&&o(a,{node:t,expanded:d,nativeEvent:n}),d&&l){var c=ke(t);c&&c.then((function(){})).catch((function(e){var n=(0,v.BA)(E.value,i);ie(n),Promise.reject(e)}))}}},Se=function(){P.value=!0},we=function(){setTimeout((function(){P.value=!1}))},De=function(n){var t=e.onActiveChange;T.value!==n&&(void 0!==e.activeKey&&(T.value=n),null!==n&&re({key:n}),t&&t(n))},Fe=(0,d.EW)((function(){return null===T.value?null:ne.value.find((function(e){var n=e.key;return n===T.value}))||null})),Oe=function(e){var n=ne.value.findIndex((function(e){var n=e.key;return n===T.value}));-1===n&&e<0&&(n=ne.value.length),n=(n+e+ne.value.length)%ne.value.length;var t=ne.value[n];if(t){var a=t.key;De(a)}else De(null)},Te=(0,d.EW)((function(){return(0,s.Hj)((0,u.A)((0,u.A)({},(0,s.N5)(T.value,U.value)),{},{data:Fe.value.data,active:!0}))})),Pe=function(n){var t=e.onKeydown,a=e.checkable,o=e.selectable;switch(n.which){case j.A.UP:Oe(-1),n.preventDefault();break;case j.A.DOWN:Oe(1),n.preventDefault();break}var l=Fe.value;if(l&&l.data){var r=!1===l.data.isLeaf||!!(l.data.children||[]).length,i=Te.value;switch(n.which){case j.A.LEFT:r&&V.value.has(T.value)?Ne({},i):l.parent&&De(l.parent.key),n.preventDefault();break;case j.A.RIGHT:r&&!V.value.has(T.value)?Ne({},i):l.children&&l.children.length&&De(l.children[0].key),n.preventDefault();break;case j.A.ENTER:case j.A.SPACE:!a||i.disabled||!1===i.checkable||i.disableCheckbox?a||!o||i.disabled||!1===i.selectable||Ke({},i):be({},i,!Z.value.has(T.value));break}}t&&t(n)};return p({onNodeExpand:Ne,scrollTo:re,onKeydown:Pe,selectedKeys:(0,d.EW)((function(){return b.value})),checkedKeys:(0,d.EW)((function(){return k.value})),halfCheckedKeys:(0,d.EW)((function(){return A.value})),loadedKeys:(0,d.EW)((function(){return m.value})),loadingKeys:(0,d.EW)((function(){return C.value})),expandedKeys:(0,d.EW)((function(){return E.value}))}),(0,d.hi)((function(){window.removeEventListener("dragend",ce),h.value=!0})),(0,c._3)({expandedKeys:E,selectedKeys:b,loadedKeys:m,loadingKeys:C,checkedKeys:k,halfCheckedKeys:A,expandedKeysSet:V,selectedKeysSet:Y,loadedKeysSet:Q,loadingKeysSet:X,checkedKeysSet:Z,halfCheckedKeysSet:q,flattenNodes:ne}),function(){var n,o,l=N.draggingNodeKey,r=N.dropLevelOffset,v=N.dropContainerKey,s=N.dropTargetKey,p=N.dropPosition,y=N.dragOverNodeKey,h=e.prefixCls,g=e.showLine,b=e.focusable,k=e.tabindex,A=void 0===k?0:k,m=e.selectable,C=e.showIcon,E=e.icon,x=void 0===E?f.icon:E,S=e.switcherIcon,w=e.draggable,F=e.checkable,P=e.checkStrictly,B=e.disabled,L=e.motion,M=e.loadData,R=e.filterTreeNode,j=e.height,$=e.itemHeight,_=e.virtual,G=e.dropIndicatorRender,z=e.onContextmenu,U=e.onScroll,V=e.direction,Y=t.class,Q=t.style,X=(0,H.A)((0,u.A)((0,u.A)({},e),t),{aria:!0,data:!0});return w&&(o="object"===(0,a.A)(w)?w:"function"===typeof w?{nodeDraggable:w}:{}),(0,d.bF)(c.Uz,{value:{prefixCls:h,selectable:m,showIcon:C,icon:x,switcherIcon:S,draggable:o,draggingNodeKey:l,checkable:F,customCheckable:f.checkable,checkStrictly:P,disabled:B,keyEntities:D.value,dropLevelOffset:r,dropContainerKey:v,dropTargetKey:s,dropPosition:p,dragOverNodeKey:y,dragging:null!==l,indent:K.value,direction:V,dropIndicatorRender:G,loadData:M,filterTreeNode:R,onNodeClick:he,onNodeDoubleClick:ge,onNodeExpand:Ne,onNodeSelect:Ke,onNodeCheck:be,onNodeLoad:ke,onNodeMouseEnter:Ae,onNodeMouseLeave:me,onNodeContextMenu:Ce,onNodeDragStart:ve,onNodeDragEnter:se,onNodeDragOver:fe,onNodeDragLeave:pe,onNodeDragEnd:de,onNodeDrop:ye,slots:f}},{default:function(){return[(0,d.bF)("div",{role:"tree",class:(0,J.A)(h,Y,(n={},(0,i.A)(n,"".concat(h,"-show-line"),g),(0,i.A)(n,"".concat(h,"-focused"),O.value),(0,i.A)(n,"".concat(h,"-active-focused"),null!==T.value),n))},[(0,d.bF)(I,(0,u.A)({ref:W,prefixCls:h,style:Q,disabled:B,selectable:m,checkable:!!F,motion:L,height:j,itemHeight:$,virtual:_,focusable:b,focused:O.value,tabindex:A,activeItem:Fe.value,onFocus:Ee,onBlur:xe,onKeydown:Pe,onActiveChange:De,onListChangeStart:Se,onListChangeEnd:we,onContextmenu:z,onScroll:U},X),null)])]}})}}})}}]);