"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[3003,4371],{44371:function(e,a,l){l.r(a),l.d(a,{default:function(){return P}});var t=l(20641);function n(e,a,l,n,i,o){const s=(0,t.g2)("sdPageHeader"),r=(0,t.g2)("DeviceList"),d=(0,t.g2)("a-tab-pane"),u=(0,t.g2)("DeviceClass"),c=(0,t.g2)("a-tabs"),p=(0,t.g2)("sdCards"),m=(0,t.g2)("Main");return(0,t.uX)(),(0,t.CE)("div",null,[(0,t.bF)(s,{title:"裝置",class:"ninjadash-page-header-main",routes:[{breadcrumbName:"測點"},{breadcrumbName:"裝置"}]}),(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{activeKey:e.activeTab,"onUpdate:activeKey":a[0]||(a[0]=a=>e.activeTab=a)},{default:(0,t.k6)((()=>[(0,t.bF)(d,{key:"1",tab:"裝置列表"},{default:(0,t.k6)((()=>["1"===e.activeTab?((0,t.uX)(),(0,t.Wv)(r,{key:0})):(0,t.Q3)("",!0)])),_:1}),(0,t.bF)(d,{key:"2",tab:"裝置分類"},{default:(0,t.k6)((()=>["2"===e.activeTab?((0,t.uX)(),(0,t.Wv)(u,{key:0})):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["activeKey"])])),_:1})])),_:1})])}var i=l(79570),o=l(79841),s=l(9322),r=l(72644);function d(e,a,l,n,i,o){const d=(0,t.g2)("a-checkbox"),u=(0,t.g2)("DataTables"),c=(0,t.g2)("sdButton"),p=(0,t.g2)("a-col"),m=(0,t.g2)("a-spin"),g=(0,t.g2)("a-row"),v=(0,t.g2)("sdModal"),b=(0,t.g2)("a-radio-group"),h=(0,t.g2)("a-form-item"),f=(0,t.g2)("a-input"),y=(0,t.g2)("a-tree-select"),k=(0,t.g2)("a-select-option"),C=(0,t.g2)("a-select"),S=(0,t.g2)("a-space"),F=(0,t.g2)("a-form"),w=(0,t.g2)("a-collapse-panel"),T=(0,t.g2)("a-collapse"),x=(0,t.g2)("Search"),_=(0,t.g2)("sdCards");return(0,t.uX)(),(0,t.CE)(t.FK,null,[e.compareModal?((0,t.uX)(),(0,t.Wv)(v,{key:0,title:`${e.compareFormState.name} 選擇測點`,visible:e.compareModal,style:{width:"100%"},onCancel:e.closeCompareModal},{default:(0,t.k6)((()=>[(0,t.bF)(d,{checked:e.showSelected,"onUpdate:checked":a[0]||(a[0]=a=>e.showSelected=a)},{default:(0,t.k6)((()=>[(0,t.eW)("只顯示勾選")])),_:1},8,["checked"]),(0,t.bF)(u,{filterOption:!0,filterOnchange:!0,tableData:e.compareTableData,columns:e.compareColumns,defaultSelected:e.compareSelected,handleDataSearch:e.searchCompareTable,onOnSelectChange:e.setTags},null,8,["tableData","columns","defaultSelected","handleDataSearch","onOnSelectChange"]),(0,t.bF)(g,{gutter:[5,20],align:"center",style:{"margin-top":"1rem"}},{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"act-btn",type:"light",onClick:(0,s.D$)(e.closeCompareModal,["prevent"])},{default:(0,t.k6)((()=>[(0,t.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"act-btn",type:"primary",disabled:e.loading,onClick:(0,s.D$)(e.compareSubmit,["prevent"])},{default:(0,t.k6)((()=>[(0,t.eW)(" 儲存 "),(0,t.bo)((0,t.bF)(m,{size:"small"},null,512),[[s.aG,e.loading]])])),_:1},8,["disabled","onClick"])])),_:1})])),_:1})])),_:1},8,["title","visible","onCancel"])):(0,t.Q3)("",!0),e.modal?((0,t.uX)(),(0,t.Wv)(v,{key:1,title:e.formState.title,visible:e.modal,onCancel:e.closeModal},{default:(0,t.k6)((()=>[(0,t.bF)(F,{model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.rules,labelAlign:"left",onFinish:e.submitForm},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"狀態",name:"status"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{value:e.formState.status,"onUpdate:value":a[1]||(a[1]=a=>e.formState.status=a),options:e.statusOptions},null,8,["value","options"])])),_:1}),(0,t.bF)(h,{label:"名稱",name:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{value:e.formState.name,"onUpdate:value":a[2]||(a[2]=a=>e.formState.name=a),placeholder:"名稱"},null,8,["value"])])),_:1}),(0,t.bF)(h,{label:"說明",name:"description"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{value:e.formState.description,"onUpdate:value":a[3]||(a[3]=a=>e.formState.description=a),placeholder:"說明"},null,8,["value"])])),_:1}),(0,t.bF)(h,{autoLink:!1,label:"地區",name:"regionId"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{value:e.formState.regionId,"onUpdate:value":a[4]||(a[4]=a=>e.formState.regionId=a),style:{width:"300px"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},"show-checked-strategy":e.SHOW_PARENT,placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data","show-checked-strategy"])])),_:1}),(0,t.bF)(h,{autoLink:!1,label:"裝置分類",name:"classId"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{value:e.formState.classId,"onUpdate:value":a[5]||(a[5]=a=>e.formState.classId=a),style:{width:"300px"},"tree-data":e.deviceClassOptions,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},"show-checked-strategy":e.SHOW_PARENT,placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data","show-checked-strategy"])])),_:1}),(0,t.bF)(h,{label:"Channel",name:"channel"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{value:e.formState.channel,"onUpdate:value":a[6]||(a[6]=a=>e.formState.channel=a)},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.channelOptions,(e=>((0,t.uX)(),(0,t.Wv)(k,{value:e.Id,key:e.Id},{default:(0,t.k6)((()=>[(0,t.eW)((0,r.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1}),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.config[e.checkChannelType],(a=>((0,t.uX)(),(0,t.Wv)(h,{key:a.value,labelCol:{sm:8},wrapperCol:{sm:16},label:a.label,name:a.value},{default:(0,t.k6)((()=>["select"===a.type?((0,t.uX)(),(0,t.Wv)(C,{key:0,value:e.formState[a.value],"onUpdate:value":l=>e.formState[a.value]=l},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(a.options,(e=>((0,t.uX)(),(0,t.Wv)(k,{value:e.Id,key:e.Id},{default:(0,t.k6)((()=>[(0,t.eW)((0,r.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value"])):(0,t.Q3)("",!0),"input"===a.type?((0,t.uX)(),(0,t.Wv)(f,{key:1,value:e.formState[a.value],"onUpdate:value":l=>e.formState[a.value]=l},null,8,["value","onUpdate:value"])):(0,t.Q3)("",!0)])),_:2},1032,["label","name"])))),128)),(0,t.bF)(g,{gutter:[0,20]},{default:(0,t.k6)((()=>[(0,t.bF)(p,{lg:{span:16,offset:8},md:{span:15,offset:9},xs:{span:24,offset:0}},{default:(0,t.k6)((()=>[(0,t.bF)(S,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"act-btn",type:"primary",disabled:e.loading,"html-type":"submit"},{default:(0,t.k6)((()=>[(0,t.eW)(" 儲存 "),(0,t.bo)((0,t.bF)(m,{size:"small"},null,512),[[s.aG,e.loading]])])),_:1},8,["disabled"]),(0,t.bF)(c,{class:"act-btn",type:"light",onClick:(0,s.D$)(e.closeModal,["prevent"])},{default:(0,t.k6)((()=>[(0,t.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,t.Q3)("",!0),(0,t.bF)(x,null,{default:(0,t.k6)((()=>[(0,t.bF)(T,{activeKey:e.collapseKey,"onUpdate:activeKey":a[10]||(a[10]=a=>e.collapseKey=a)},{default:(0,t.k6)((()=>[(0,t.bF)(w,{key:"1",header:"條件篩選"},{default:(0,t.k6)((()=>[(0,t.bF)(S,{direction:"vertical",size:"middle"},{default:(0,t.k6)((()=>[(0,t.eW)(" 地區: "),(0,t.bF)(y,{value:e.filterFormState.region,"onUpdate:value":a[7]||(a[7]=a=>e.filterFormState.region=a),style:{width:"300px"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},"show-checked-strategy":e.SHOW_PARENT,placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data","show-checked-strategy"]),(0,t.eW)(" 裝置分類: "),(0,t.bF)(y,{value:e.filterFormState.class,"onUpdate:value":a[8]||(a[8]=a=>e.filterFormState.class=a),style:{width:"300px"},"tree-data":e.deviceClassOptions,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},"show-checked-strategy":e.SHOW_PARENT,placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data","show-checked-strategy"]),(0,t.eW)(" Channel: "),(0,t.bF)(C,{value:e.filterFormState.targetChannel,"onUpdate:value":a[9]||(a[9]=a=>e.filterFormState.targetChannel=a),style:{width:"150px"}},{default:(0,t.k6)((()=>[(0,t.bF)(k,{value:null},{default:(0,t.k6)((()=>[(0,t.eW)("無")])),_:1}),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.channelOptions,(e=>((0,t.uX)(),(0,t.Wv)(k,{value:e.Id,key:e.Id},{default:(0,t.k6)((()=>[(0,t.eW)((0,r.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1},8,["activeKey"])])),_:1}),(0,t.bF)(_,null,{default:(0,t.k6)((()=>[e.loading?((0,t.uX)(),(0,t.Wv)(m,{key:0})):(0,t.Q3)("",!0),(0,t.bF)(u,{filterOption:!0,filterOnchange:!0,tableData:e.data,columns:e.columns,rowSelection:!1,addOption:e.permission.create,handleDataSearch:e.search,handleAdd:e.openAddModal},null,8,["tableData","columns","addOption","handleDataSearch","handleAdd"])])),_:1})],64)}l(75126);var u=l(56427),c=(l(1532),l(30995)),p=(l(6070),l(7179)),m=(l(44114),l(18111),l(22489),l(20116),l(7588),l(61701),l(40834)),g=l(94734),v=l(45118),b=l(95853);const h=b.Ay.div`
    display:flex;
    justify-content:end;
    svg{
       height:20px;
       cursor:pointer;
       margin:0 5px;
    }
`,f=(b.Ay.div`
    .act-btn{
        margin:0 5px;
    }
`,b.Ay.div`
    .ant-collapse{
        border:0
    }
    .ant-collapse-header{
        background-color:transparent !important;
        color: ${({theme:e})=>e["primary-color"]} !important;
    }
`);var y=l(82958),k=l(18683),C=l(74353),S=l.n(C),F=l(96763),w=(0,t.pM)({components:{LevelSelect:g.A,DataTables:v.A,Search:f},setup(){const{permission:e}=(0,y.J)(),a=p.Ay.SHOW_PARENT,{dispatch:l,state:n}=(0,m.Pj)(),i=(0,t.EW)((()=>n.device.loading)),s=(0,o.KR)("1");(0,t.sV)((async()=>{const e=await Promise.all([l("device/getAllDeviceAndOptions")]);T.value=e[0].locations,v.value=e[0].rateOptions,b.value=e[0].channelOptions,f.value=e[0].dataFormateOptions,C.value=e[0].addressStartOptions,w.value=e[0].deviceClassOptions,r.value={TCP:[{label:"資料格式",type:"select",value:"dataFormate",options:e[0].dataFormateOptions},{label:"資料位址起始值",type:"select",value:"addressStart",options:e[0].addressStartOptions},{label:"IP",type:"input",value:"ip"},{label:"Port",type:"input",value:"TCPPort"},{label:"站號",type:"input",value:"station"}],OBIX:[{label:"Endpoint",type:"input",value:"endpoint"},{label:"使用者帳號",type:"input",value:"username"},{label:"使用者密碼",type:"input",value:"password"}],"Desigo CC":[{label:"web應用程式名稱",type:"input",value:"webAppName"},{label:"系統名稱",type:"input",value:"systemName"},{label:"系統編號",type:"input",value:"systemId"},{label:"使用者帳號",type:"input",value:"username"},{label:"使用者密碼",type:"input",value:"password"},{label:"Endpoint",type:"input",value:"endpoint"}]},g.value=!0})),(0,t.xo)((()=>{l("device/resetState")}));const r=(0,o.KR)(),d=[4,5],g=(0,o.KR)(!1),v=(0,o.KR)([]),b=(0,o.KR)([]),f=(0,o.KR)([]),C=(0,o.KR)([]),w=(0,o.KR)([]),T=(0,o.KR)([]),x=[{value:!0,label:"啟用"},{value:!1,label:"停用"}],_=(0,o.Kh)({region:null,class:null,targetChannel:null,text:null}),D=[{title:"名稱",dataIndex:"DeviceName",key:"DeviceName",sorter:(e,a)=>e.DeviceName.localeCompare(a.DeviceName),fixed:"left"},{title:"Channel",dataIndex:"channelName",key:"channelName",sorter:(e,a)=>e.channelName.localeCompare(a.channelName)},{title:"地區",dataIndex:"locationName",key:"locationName",sorter:(e,a)=>e.locationName.localeCompare(a.locationName)},{title:"狀態",dataIndex:"statusName",key:"statusName"},{title:"上次匯入時間",dataIndex:"lastImportTime",key:"lastImportTime"},{title:"匯入狀態",dataIndex:"importStatus",key:"importStatus"},{title:"操作",dataIndex:"action",key:"action"}],N=()=>{const e=[{type:"text",target:_.text},{type:"list",target:_.region?_.region:null,source:"RegionList",sourceProp:"Id"},{type:"list",target:_.class?_.class:null,source:"DeviceCategoryList",sourceProp:"Id"},{type:"element",target:_.targetChannel,source:"TagChannel",sourceProp:"Id"}];l("device/filterTagsDeviceTable",e)};(0,t.wB)((()=>_),N,{deep:!0}),(0,t.wB)((()=>n.device.deviceInitData),N,{deep:!0});const I=(0,t.EW)((()=>g.value?n.device.deviceTableData.map((a=>({...a,lastImportTime:d.includes(a.TagChannel.TypeCode)?a.LastDownLoadTagTime?S()(a.LastDownLoadTagTime).format("YYYY-MM-DD HH:mm:ss"):"未匯入":null,importStatus:d.includes(a.TagChannel.TypeCode)?a.DownLoadTagOK?"已完成":"匯入中":null,locationName:a.RegionList.map((e=>e.Name)).join(" > "),channelName:a.TagChannel.Name,statusName:x.find((e=>e.value===a.Status)).label,action:(0,t.bF)(h,null,{default:()=>[e.update&&d.includes(a.TagChannel.TypeCode)&&(0,t.bF)("span",{onClick:()=>q(a.DeviceId,a.DownLoadTagOK)},[(0,t.bF)((0,t.g2)("unicon"),{name:"refresh"},null)]),e.update&&d.includes(a.TagChannel.TypeCode)&&(0,t.bF)("span",{onClick:()=>Z({id:a.DeviceId,importDone:a.DownLoadTagOK,name:a.DeviceName})},[(0,t.bF)((0,t.g2)("unicon"),{name:"file-check-alt"},null)]),e.update&&(0,t.bF)("span",{onClick:()=>X(a.DeviceId)},[(0,t.bF)((0,t.g2)("unicon"),{name:"edit"},null)]),e.delete&&(0,t.bF)("span",{onClick:()=>U(a.DeviceId)},[(0,t.bF)((0,t.g2)("unicon"),{name:"trash"},null)])]})}))):[])),A=e=>{_.text=e.target.value},O=(0,o.KR)(!1),W=(0,o.Kh)({title:"",id:null,name:null,description:null,channel:null,channelType:null,status:null,regionId:null,classId:null,ip:null,TCPPort:null,rate:null,RTUPort:null,station:null,dataFormate:null,addressStart:null,endpoint:null,username:null,password:null,webAppName:null,systemName:null,systemId:null}),R={lg:8,md:9,xs:24},P={lg:16,md:15,xs:24},K=(0,t.EW)((()=>{if(W.channel){const e=b.value.find((e=>e.Id===W.channel));return e.TypeName}return null})),L={name:[{required:!0,message:"請輸入名稱",trigger:"blur"}],description:[{required:!0,message:"請輸入說明",trigger:"blur"}],channel:[{required:!0,message:"請選擇",trigger:"blur"}],regionId:[{required:!0,message:"請選擇",trigger:"blur"}],ip:[{required:!0,message:"請輸入IP",trigger:"blur"}],TCPPort:[{required:!0,message:"請輸入Port",trigger:"blur"}],RTUPort:[{required:!0,message:"請輸入Port",trigger:"blur"}],rate:[{required:!0,message:"請選擇包率",trigger:"blur"}],station:[{required:!0,message:"請輸入站號",trigger:"blur"}],dataFormate:[{required:!0,message:"請選擇格式",trigger:"blur"}],addressStart:[{required:!0,message:"請選擇",trigger:"blur"}],endpoint:[{required:!0,message:"請輸入Endpoint",trigger:"blur"}],username:[{required:!0,message:"請輸入帳號",trigger:"blur"}],password:[{required:!0,message:"請輸入密碼",trigger:"blur"}],webAppName:[{required:!0,message:"請輸入名稱",trigger:"blur"}],systemName:[{required:!0,message:"請輸入名稱",trigger:"blur"}],systemId:[{required:!0,message:"請輸入ID",trigger:"blur"}]},E=()=>{const e={title:"新增裝置",id:null,name:null,description:null,dataFormate:null,addressStart:null,channel:null,channelType:null,ip:null,TCPPort:null,rate:null,RTUPort:null,station:null,status:!0,regionId:null,classId:null,endpoint:null,username:null,password:null};Object.assign(W,e),O.value=!0},X=e=>{W.title="編輯裝置";const a=I.value.find((a=>a.DeviceId===e));W.id=a.DeviceId,W.name=a.DeviceName,W.description=a.Description,W.channel=a.TagChannel.Id,W.channelType=a.TagChannel.TypeName,W.ip=a.Ip,W.TCPPort=a.Port,W.rate=a.BaudRate,W.RTUPort=a.RtuPort,W.station=a.StationNo,W.dataFormate=a.DeviceDataFormat.Id,W.addressStart=a.DeviceDataAddressFrom.Id,W.regionId=a.RegionList[a.RegionList.length-1].Id,W.endpoint=""!==a.ObixProtocalPrefix?a.ObixProtocalPrefix:a.EndPoint,W.username=a.UserName,W.password=a.Password,W.webAppName=a.WebAppName,W.systemName=a.SystemName,W.systemId=a.SystemId,W.classId=a.DeviceCategoryList[a.DeviceCategoryList.length-1].Id,W.status=a.Status,F.log(a.TagChannel.TypeName),O.value=!0},M=()=>{O.value=!1},U=e=>{c.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:i.value,onOk:async()=>{try{await l("device/deleteDevice",e),u.A.success({message:"刪除成功"})}catch(a){c.A.error({title:"發生錯誤",content:a.message})}}})},z=async()=>{try{let e;W.id?(await l("device/editDevice",W),e="修改成功"):(await l("device/addDevice",W),e="新增成功"),O.value=!1,u.A.success({message:e})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}},q=async(e,a)=>{a?c.A.confirm({title:"確認匯入?",okText:"確認",cancelText:"取消",confirmLoading:i.value,onOk:async()=>{try{await l("device/importTag",e),c.A.success({content:"已開始匯入程序"})}catch(a){c.A.error({title:"發生錯誤",content:a.message})}}}):c.A.error({title:"發生錯誤",content:"資料已在匯入中，請勿重複匯入"})},j=(0,o.KR)(!1),B=(0,o.KR)(""),Q=(0,o.Kh)({id:null,name:null,tags:[]}),$=(0,o.KR)(!1),H=(0,o.KR)([]),J=(0,o.KR)(!1),V=(0,t.EW)((()=>{const e=(0,k.AQ)(H.value,B.value);return j.value?e.filter((e=>Y.value.includes(e.Name))):e})),G=[{title:"名稱",dataIndex:"Name",key:"Name"},{title:"描述",dataIndex:"Description",key:"Description"},{title:"地區",dataIndex:"Location",key:"Location",align:"left"}],Y=(0,t.EW)((()=>Q.tags)),Z=async({id:e,importDone:a,name:t})=>{if(a)try{const a=await l("device/getCompareTable",e);Q.id=e,Q.name=t,H.value=a.map((e=>({...e,key:e.Name})));const n=[];H.value.forEach((e=>{e.Selected&&n.push(e.Name)})),Q.tags=n,$.value=!0}catch(n){c.A.error({title:"發生錯誤",content:n.message})}else c.A.error({title:"發生錯誤",content:"測點匯入中，請稍後"})},ee=e=>{J.value=!0,B.value=e.target.value,J.value=!1},ae=e=>{const a=V.value.map((e=>e.Name)),l=Q.tags.filter((e=>!a.includes(e))),t=l.concat(e);Q.tags=t},le=()=>{$.value=!1,B.value=""},te=async()=>{try{const e={id:Q.id,tags:Q.tags.map((e=>H.value.find((a=>a.Name===e))))};await l("device/compareTags",e),u.A.success({message:"成功導入測點"})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}};return{config:r,permission:e,SHOW_PARENT:a,loading:i,collapseKey:s,locations:T,rateOptions:v,channelOptions:b,dataFormateOptions:f,addressStartOptions:C,deviceClassOptions:w,statusOptions:x,filterFormState:_,modal:O,formState:W,checkChannelType:K,rules:L,columns:D,data:I,search:A,labelCol:R,wrapperCol:P,openAddModal:E,openEditModal:X,closeModal:M,submitForm:z,compareModal:$,closeCompareModal:le,showSelected:j,compareTableData:V,compareColumns:G,compareSelected:Y,searchCompareTable:ee,setTags:ae,compareTag:Z,compareSubmit:te,compareFormState:Q,compareLoading:J}}}),T=l(66262);const x=(0,T.A)(w,[["render",d]]);var _=x;function D(e,a,l,n,i,o){const r=(0,t.g2)("a-input"),d=(0,t.g2)("a-form-item"),u=(0,t.g2)("a-spin"),c=(0,t.g2)("sdButton"),p=(0,t.g2)("a-col"),m=(0,t.g2)("a-row"),g=(0,t.g2)("a-form"),v=(0,t.g2)("ModalWrap"),b=(0,t.g2)("sdModal"),h=(0,t.g2)("DataTables"),f=(0,t.g2)("sdCards");return(0,t.uX)(),(0,t.CE)("div",null,[e.modal?((0,t.uX)(),(0,t.Wv)(b,{key:0,title:e.formState.title,visible:e.modal,onCancel:e.closeModal},{default:(0,t.k6)((()=>[(0,t.bF)(v,null,{default:(0,t.k6)((()=>[(0,t.bF)(g,{model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.rules,labelAlign:"left",onFinish:e.submit},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"分類名稱",name:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{value:e.formState.name,"onUpdate:value":a[0]||(a[0]=a=>e.formState.name=a),placeholder:"名稱"},null,8,["value"])])),_:1}),(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(p,{lg:{span:16,offset:8},md:{span:15,offset:9},xs:{span:24,offset:0}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",null,[(0,t.bF)(c,{class:"act-btn",type:"primary","html-type":"submit",disabled:e.loading},{default:(0,t.k6)((()=>[(0,t.eW)(" 儲存 "),(0,t.bo)((0,t.bF)(u,{size:"small"},null,512),[[s.aG,e.loading]])])),_:1},8,["disabled"]),(0,t.bF)(c,{class:"act-btn",type:"light",onClick:(0,s.D$)(e.closeModal,["prevent"])},{default:(0,t.k6)((()=>[(0,t.eW)(" 取消 ")])),_:1},8,["onClick"])])])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1})])),_:1},8,["title","visible","onCancel"])):(0,t.Q3)("",!0),(0,t.bF)(f,{title:"分類列表"},{default:(0,t.k6)((()=>[e.loading?((0,t.uX)(),(0,t.Wv)(u,{key:0})):(0,t.Q3)("",!0),e.loading?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(h,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:e.permission.create,backOption:e.isChild,handleBack:e.goBack,handleAdd:e.openAddModal,handleDataSearch:e.search,backTitle:e.backTitle},null,8,["tableData","columns","addOption","backOption","handleBack","handleAdd","handleDataSearch","backTitle"]))])),_:1})])}b.Ay.div`
    display:flex;
    justify-content:end;
    svg{
       height:20px;
       cursor:pointer;
       margin:0 5px;
    }
`;const N=b.Ay.div`
    .act-btn{
        margin:0 5px;
    }
`;var I=(0,t.pM)({components:{DataTables:v.A,ModalWrap:N},setup(){const{permission:e}=(0,y.J)(),a=(0,t.EW)((()=>n.device.loading)),{dispatch:l,state:n}=(0,m.Pj)();(0,t.sV)((()=>{l("device/getAllClass")}));const s=(0,o.KR)(!1),r=(0,o.Kh)({title:null,id:null,parentId:null,name:null}),d={name:[{required:!0,message:"請輸入名稱",trigger:"blur"}],description:[{required:!0,message:"請填入說明",trigger:"blur"}]},p={lg:8,md:9,xs:24},g={lg:16,md:15,xs:24},v=()=>{s.value=!1},b=async()=>{try{let e;r.id?(await l("device/editClass",r),e="修改成功"):(await l("device/addClass",r),e="新增成功"),s.value=!1,u.A.success({message:e})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}},h=[{title:"名稱",dataIndex:"name",key:"name"},{title:"操作",dataIndex:"action",key:"action"}],f=(0,t.EW)((()=>n.device.classURLs.length>0)),k=(0,t.EW)((()=>n.device.classTableData?n.device.classTableData.map((a=>({name:(0,t.bF)(i.N_,{onClick:()=>F({id:a.Id,name:a.Name})},{default:()=>[a.Name]}),action:(0,t.bF)(i.J9,null,{default:()=>[e.update&&(0,t.bF)("span",{onClick:()=>x(a)},[(0,t.bF)((0,t.g2)("unicon"),{name:"edit"},null)]),e.delete&&(0,t.bF)("span",{onClick:()=>_(a.Id)},[(0,t.bF)((0,t.g2)("unicon"),{name:"trash"},null)])]})}))):[])),C=e=>{l("device/filterClassTable",e.target.value)},S=(0,t.EW)((()=>n.device.classURLs.length>0&&n.device.classURLs[n.device.classURLs.length-1].name)),F=async({id:e,name:a})=>{try{await l("device/getClassChild",{id:e,name:a})}catch(t){c.A.error({title:"發生錯誤",content:t.message})}},w=async()=>{await l("device/classGoback")},T=()=>{r.title="新增分類",r.id=null,r.name=null,r.parentId=n.device.classURLs[n.device.classURLs.length-1],s.value=!0},x=({Id:e,Name:a})=>{r.title="編輯分類",r.id=e,r.name=a,r.parentId=null,s.value=!0},_=e=>{c.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:a.value,onOk:async()=>{try{await l("device/deleteClass",e),u.A.success({message:"刪除成功"})}catch(a){c.A.error({title:"發生錯誤",content:a.message})}}})};return{backTitle:S,permission:e,loading:a,modal:s,formState:r,rules:d,submit:b,labelCol:p,wrapperCol:g,columns:h,isChild:f,tableData:k,search:C,closeModal:v,goBack:w,openAddModal:T}}});const A=(0,T.A)(I,[["render",D]]);var O=A,W=(0,t.pM)({components:{DeviceList:_,DeviceClass:O,Main:i.gZ},setup(){const e=(0,o.KR)("1");return{activeTab:e}}});const R=(0,T.A)(W,[["render",n]]);var P=R},45118:function(e,a,l){l.d(a,{A:function(){return h}});var t=l(20641),n=l(72644);const i={class:"ninjadash-datatable-filter"},o={key:0,class:"ninjadash-datatable-filter__right"},s={class:"ninjadasj-datatable"};function r(e,a,l,r,d,u){const c=(0,t.g2)("sdButton"),p=(0,t.g2)("a-space"),m=(0,t.g2)("unicon"),g=(0,t.g2)("a-input"),v=(0,t.g2)("a-button"),b=(0,t.g2)("a-col"),h=(0,t.g2)("a-row"),f=(0,t.g2)("a-table"),y=(0,t.g2)("TableWrapper"),k=(0,t.g2)("DataTableStyleWrap");return(0,t.uX)(),(0,t.Wv)(k,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.Lk)("div",null,[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[e.addOption?((0,t.uX)(),(0,t.Wv)(c,{key:0,class:"act-btn",type:"primary",onClick:e.handleAdd},{default:(0,t.k6)((()=>[(0,t.eW)(" 新增 ")])),_:1},8,["onClick"])):(0,t.Q3)("",!0),e.backOption?((0,t.uX)(),(0,t.Wv)(c,{key:1,size:"default",outlined:!0,type:"primary",onClick:e.handleBack},{default:(0,t.k6)((()=>[(0,t.eW)(" 回上層 "+(0,n.v_)(e.backTitle),1)])),_:1},8,["onClick"])):(0,t.Q3)("",!0)])),_:1})]),e.filterOption?((0,t.uX)(),(0,t.CE)("div",o,[(0,t.bF)(g,{onChange:e.handleDataSearch,size:"default",placeholder:"搜尋"},{prefix:(0,t.k6)((()=>[(0,t.bF)(m,{name:"search"})])),_:1},8,["onChange"])])):(0,t.Q3)("",!0)]),(0,t.bF)(h,{align:"end"},{default:(0,t.k6)((()=>[e.importOption?((0,t.uX)(),(0,t.Wv)(b,{key:0,style:{"margin-bottom":"1rem"}},{default:(0,t.k6)((()=>[(0,t.bF)(v,{type:"primary",ghost:"",onClick:e.handleImport},{default:(0,t.k6)((()=>[(0,t.eW)("匯入")])),_:1},8,["onClick"])])),_:1})):(0,t.Q3)("",!0),e.exportOption?((0,t.uX)(),(0,t.Wv)(b,{key:1,style:{"margin-bottom":"1rem"}},{default:(0,t.k6)((()=>[(0,t.bF)(v,{type:"primary",ghost:"",onClick:e.handleExport},{default:(0,t.k6)((()=>[(0,t.eW)("匯出Excel")])),_:1},8,["onClick"])])),_:1})):(0,t.Q3)("",!0)])),_:1}),(0,t.Lk)("div",s,[(0,t.bF)(y,{class:"table-data-view table-responsive"},{default:(0,t.k6)((()=>[e.rowSelection?((0,t.uX)(),(0,t.Wv)(f,{key:0,class:"ant-table-striped","row-selection":e.rowSelections,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},childrenColumnName:e.childrenColumnName,"row-class-name":e.getRowClassName,"data-source":e.tableData,columns:e.columns,onChange:e.changePageSize},null,8,["row-selection","pagination","childrenColumnName","row-class-name","data-source","columns","onChange"])):((0,t.uX)(),(0,t.Wv)(f,{key:1,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},class:"ant-table-striped",childrenColumnName:e.childrenColumnName,"data-source":e.tableData,"row-class-name":e.getRowClassName,columns:e.columns,onChange:e.changePageSize},null,8,["pagination","childrenColumnName","data-source","row-class-name","columns","onChange"]))])),_:1})])])),_:1})}var d=l(79841),u=l(19732),c=l(95853);const p=c.Ay.div`
    .ninjadash-datatable-filter{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 20px 0 25px 0;
        @media only screen and (max-width: 575px){
            flex-wrap: wrap;
        }
        .ninjadash-datatable-filter__left{
            display: inline-flex;
            width: 100%;
            align-items: center;
            .ant-form{
                display: inline-flex;
                width: 100%;
                align-items: center;
            }
            span.label{
                margin-right: 8px;
                color: ${({theme:e})=>e[e.mainContent]["gray-text"]};
            }
            .ninjadash-datatable-filter__input{
                display: flex;
                align-items: center;
                padding-right: 20px;
                .ant-input{
                    height: 40px;
                }
            }
        }
        .ninjadash-datatable-filter__right{
            min-width: 280px;
            @media only screen and (max-width: 575px){
                margin-top: 15px;
            }
            .ant-input-affix-wrapper{
                padding: 7.22px 20px;
                border-radius: 6px;
                .ant-input-prefix{
                    svg{
                        width: 16px;
                        height: 16px;
                        fill: ${({theme:e})=>e[e.mainContent]["light-text"]};
                    }
                }
            }
        }
    }
`;var m=l(79570),g=(0,t.pM)({components:{DataTableStyleWrap:p,TableWrapper:m.AC},props:{filterOption:u.Ay.bool,filterOnchange:u.Ay.bool,rowSelection:u.Ay.bool,defaultSelected:u.Ay.array,tableData:u.Ay.array,columns:u.Ay.array,handleDataSearch:u.Ay.func,handleAdd:u.Ay.func,handleBack:u.Ay.func,handleImport:u.Ay.func,handleExport:u.Ay.func,rowClassFunc:{type:Function,default:()=>{}},backOption:{type:Boolean,default:!1},addOption:{type:Boolean,default:!1},exportOption:{type:Boolean,default:!1},importOption:{type:Boolean,default:!1},expandedRow:{type:Object,default:null},childrenColumnName:{type:String,default:"children"},backTitle:{type:String,default:""}},setup(e,{emit:a}){const l=(0,d.KR)([]);(0,t.wB)((()=>e.defaultSelected),(e=>{l.value=e}),{immediate:!0});const n=e=>{l.value=e,a("onSelectChange",l.value)},i=(0,t.EW)((()=>({selectedRowKeys:(0,d.R1)(l),onChange:n,hideDefaultSelections:!0}))),o=(0,d.KR)(10),s=(0,d.KR)(["10","20","50","100"]),r=e=>{o.value=e.pageSize},u=({record:a})=>e.expandedRow.innerDataProp?a[e.expandedRow.innerDataProp]:a.children,c=(a,l)=>e.rowClassFunc(a)??l%2===1?"table-striped row-style":"row-style";return{pageSize:o,pageSizeOptions:s,rowSelections:i,changePageSize:r,getInnerData:u,getRowClassName:c}}}),v=l(66262);const b=(0,v.A)(g,[["render",r],["__scopeId","data-v-1b881e36"]]);var h=b},75126:function(e,a,l){l(16859)},82958:function(e,a,l){l.d(a,{J:function(){return o}});var t=l(79841),n=l(92317),i=l(75220);function o(e){const{name:a}=(0,i.lq)();let l="";l=e||a;const o=(0,n.Gq)("permission"),s=(0,t.Kh)({read:o[l]?.includes("r"),create:o[l]?.includes("c"),update:o[l]?.includes("u"),delete:o[l]?.includes("d")});return{permission:s}}},94734:function(e,a,l){l.d(a,{A:function(){return p}});var t=l(20641),n=l(9322),i=l(72644);function o(e,a,l,o,s,r){const d=(0,t.g2)("a-select-option"),u=(0,t.g2)("a-select"),c=(0,t.g2)("a-space");return(0,t.uX)(),(0,t.CE)("div",null,[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.levels,(l=>((0,t.uX)(),(0,t.Wv)(u,{value:e.selected[l],"onUpdate:value":a=>e.selected[l]=a,key:l,style:{width:"100%","min-width":"100px"},onChange:a=>e.handleChange(l)},{default:(0,t.k6)((()=>[e.nullOption?((0,t.uX)(),(0,t.Wv)(d,{key:0,value:null,onClick:a[0]||(a[0]=(0,n.D$)((()=>{}),["stop"]))},{default:(0,t.k6)((()=>[(0,t.eW)("無")])),_:1})):(0,t.Q3)("",!0),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.getNextLevel(l),((l,o)=>((0,t.uX)(),(0,t.Wv)(d,{value:JSON.stringify(l),key:o,onClick:a[1]||(a[1]=(0,n.D$)((()=>{}),["stop"]))},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(l[e.childName]),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","onChange"])))),128))])),_:1})])}l(44114);var s=l(79841),r=l(19732),d=(0,t.pM)({name:"LevelSelect",props:{selectedValue:{type:Object,default:null},group:r.Ay.array,nullOption:{type:Boolean,default:!1},childName:{type:String,default:"name"},childProp:{type:String,default:"children"}},setup(e,{emit:a}){const l=(0,s.KR)(e.selectedValue||{}),n=(0,s.KR)(e.selectedValue?Array.from(Array(Object.keys(e.selectedValue).length).keys()):[0]),i=(0,t.EW)((()=>e.group?e.group:[])),o=a=>{if(0===a)return i.value;const t=n.value[a-1],o=JSON.parse(l.value[t]);return o&&o[e.childProp]&&o[e.childProp].length>0?o[e.childProp]:[]},r=t=>{const i=n.value.indexOf(t);for(let e=i+1;e<n.value.length;e++)delete l.value[e];if(n.value.splice(i+1,n.value.length-(i+1)),l.value[t]){a("change",JSON.parse(l.value[n.value.length-1]),l.value);const i=JSON.parse(l.value[t]);i[e.childProp]&&n.value.push(t+1)}else{const e=n.value.length>1?l.value[n.value.length-2]:null;a("change",JSON.parse(e),l.value)}};return{selected:l,levels:n,getNextLevel:o,handleChange:r}}}),u=l(66262);const c=(0,u.A)(d,[["render",o]]);var p=c}}]);