{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.Extensions.TimeProvider.Testing >= 8.5.0", "Microsoft.NET.Test.Sdk >= 17.10.0", "NSubstitute >= 5.1.0", "coverlet.collector >= 6.0.2", "xunit >= 2.8.0", "xunit.runner.visualstudio >= 2.8.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars.UnitTests\\Oco.Core.Vars.UnitTests.csproj", "projectName": "Oco.Core.Vars.UnitTests", "projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars.UnitTests\\Oco.Core.Vars.UnitTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars.UnitTests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.TimeProvider.Testing": {"target": "Package", "version": "[8.5.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.10.0, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.8.0, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1803", "level": "Warning", "warningLevel": 1, "message": "You are running the 'restore' operation with an 'HTTP' source, 'http://192.168.1.154:14235/v3/index.json'. Non-HTTPS access will be removed in a future version. Consider migrating to an 'HTTPS' source."}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "coverlet.collector"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.NET.Test.Sdk"}]}