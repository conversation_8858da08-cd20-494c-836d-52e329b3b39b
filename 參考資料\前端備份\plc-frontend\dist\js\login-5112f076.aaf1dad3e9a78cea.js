(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[321],{62337:function(t,e,r){!function(e,r){t.exports=r()}(0,(function(){"use strict";var t="9.28.0",e=Object.prototype.toString;function o(t,e,r,o){if(r="array"===r?"object":r,t&&typeof t[e]!==r)throw new Error(o)}function n(t,e,r){if(typeof t!==e)throw new Error(r)}function i(t,e,r){if(-1===e.indexOf(t))throw new Error(r)}var a={check:function(t,e,r){if(e.optional&&!t||n(t,e.type,e.message),"object"===e.type&&r)for(var a=Object.keys(r),p=0;p<a.length;p++){var u=a[p];r[u].optional&&!t[u]||r[u].condition&&!r[u].condition(t)||(o(t,u,r[u].type,r[u].message),r[u].values&&i(t[u],r[u].values,r[u].value_message))}},attribute:o,variable:n,value:i,isArray:function(t){return this.supportsIsArray()?Array.isArray(t):"[object Array]"===e.call(t)},supportsIsArray:function(){return null!=Array.isArray}};function p(t){if(null==t)throw new TypeError("Cannot convert first argument to object");for(var e=Object(t),r=1;r<arguments.length;r++){var o=arguments[r];if(null!=o)for(var n=Object.keys(Object(o)),i=0,a=n.length;i<a;i++){var p=n[i],u=Object.getOwnPropertyDescriptor(o,p);void 0!==u&&u.enumerable&&(e[p]=o[p])}}return e}var u={get:function(){return Object.assign?Object.assign:p},objectAssignPolyfill:p};function l(t,e){return e.reduce((function(e,r){return t[r]&&(e[r]=t[r]),e}),{})}function c(t){var e=[];for(var r in t)e.push(t[r]);return e}function f(){var t=c(arguments);return t.unshift({}),u.get().apply(void 0,t)}function y(t){var e=t.match(/^(https?:|file:|chrome-extension:)\/\/(([^:/?#]*)(?::([0-9]+))?)([/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/);return e&&{href:t,protocol:e[1],host:e[2],hostname:e[3],port:e[4],pathname:e[5],search:e[6],hash:e[7]}}function s(t,e){var r=f(t);return t[e]&&(r[e]=t[e].trim()),r}var d={toSnakeCase:function t(e,r){return"object"!=typeof e||a.isArray(e)||null===e?e:(r=r||[],Object.keys(e).reduce((function(o,n){return o[-1===r.indexOf(n)?function(t){for(var e,r="",o=0,n=!0,i=!0;o<t.length;)e=t.charCodeAt(o),!i&&e>=65&&e<=90||!n&&e>=48&&e<=57?(r+="_",r+=t[o].toLowerCase()):r+=t[o].toLowerCase(),n=e>=48&&e<=57,i=e>=65&&e<=90,o++;return r}(n):n]=t(e[n]),o}),{}))},toCamelCase:function t(e,r,o){return"object"!=typeof e||a.isArray(e)||null===e?e:(r=r||[],o=o||{},Object.keys(e).reduce((function(n,i){var a,p=-1===r.indexOf(i)?(a=i.split("_")).reduce((function(t,e){return t+e.charAt(0).toUpperCase()+e.slice(1)}),a.shift()):i;return n[p]=t(e[p]||e[i],[],o),o.keepOriginal&&(n[i]=t(e[i],[],o)),n}),{}))},blacklist:function(t,e){return Object.keys(t).reduce((function(r,o){return-1===e.indexOf(o)&&(r[o]=t[o]),r}),{})},merge:function(t,e){return{base:e?l(t,e):t,with:function(t,e){return t=e?l(t,e):t,f(this.base,t)}}},pick:l,getKeysNotIn:function(t,e){var r=[];for(var o in t)-1===e.indexOf(o)&&r.push(o);return r},extend:f,getOriginFromUrl:function(t){if(t){var e=y(t);if(!e)return null;var r=e.protocol+"//"+e.hostname;return e.port&&(r+=":"+e.port),r}},getLocationFromUrl:y,trimUserDetails:function(t){return function(t,e){return e.reduce(s,t)}(t,["username","email","phoneNumber"])},updatePropertyOn:function t(e,r,o){"string"==typeof r&&(r=r.split("."));var n=r[0];e.hasOwnProperty(n)&&(1===r.length?e[n]=o:t(e[n],r.slice(1),o))}};function h(){return window}var g={redirect:function(t){h().location=t},getDocument:function(){return h().document},getWindow:h,getOrigin:function(){var t=h().location,e=t.origin;return e||(e=d.getOriginFromUrl(t.href)),e}},b="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof r.g?r.g:"undefined"!=typeof self?self:{};function v(t,e){return t(e={exports:{}},e.exports),e.exports}var m=v((function(t){var e,r;e=b,r=function(){function t(t){var e=[];if(0===t.length)return"";if("string"!=typeof t[0])throw new TypeError("Url must be a string. Received "+t[0]);if(t[0].match(/^[^/:]+:\/*$/)&&t.length>1){var r=t.shift();t[0]=r+t[0]}t[0].match(/^file:\/\/\//)?t[0]=t[0].replace(/^([^/:]+):\/*/,"$1:///"):t[0]=t[0].replace(/^([^/:]+):\/*/,"$1://");for(var o=0;o<t.length;o++){var n=t[o];if("string"!=typeof n)throw new TypeError("Url must be a string. Received "+n);""!==n&&(o>0&&(n=n.replace(/^[\/]+/,"")),n=o<t.length-1?n.replace(/[\/]+$/,""):n.replace(/[\/]+$/,"/"),e.push(n))}var i=e.join("/"),a=(i=i.replace(/\/(\?|&|#[^!])/g,"$1")).split("?");return a.shift()+(a.length>0?"?":"")+a.join("&")}return function(){return t("object"==typeof arguments[0]?arguments[0]:[].slice.call(arguments))}},t.exports?t.exports=r():e.urljoin=r()})),w=Error,S=EvalError,A=RangeError,j=ReferenceError,O=SyntaxError,P=TypeError,E=URIError,_="undefined"!=typeof Symbol&&Symbol,x={foo:{}},k=Object,I="Function.prototype.bind called on incompatible ",F=Object.prototype.toString,R=Math.max,D=function(t,e){for(var r=[],o=0;o<t.length;o+=1)r[o]=t[o];for(var n=0;n<e.length;n+=1)r[n+t.length]=e[n];return r},U=function(t,e){for(var r=[],o=e||0,n=0;o<t.length;o+=1,n+=1)r[n]=t[o];return r},M=function(t,e){for(var r="",o=0;o<t.length;o+=1)r+=t[o],o+1<t.length&&(r+=e);return r},T=Function.prototype.bind||function(t){var e=this;if("function"!=typeof e||"[object Function]"!==F.apply(e))throw new TypeError(I+e);for(var r,o=U(arguments,1),n=function(){if(this instanceof r){var n=e.apply(this,D(o,arguments));return Object(n)===n?n:this}return e.apply(t,D(o,arguments))},i=R(0,e.length-o.length),a=[],p=0;p<i;p++)a[p]="$"+p;if(r=Function("binder","return function ("+M(a,",")+"){ return binder.apply(this,arguments); }")(n),e.prototype){var u=function(){};u.prototype=e.prototype,r.prototype=new u,u.prototype=null}return r},N=Function.prototype.call,C=Object.prototype.hasOwnProperty,W=T.call(N,C),B=Function,H=function(t){try{return B('"use strict"; return ('+t+").constructor;")()}catch(e){}},L=Object.getOwnPropertyDescriptor;if(L)try{L({},"")}catch(Ye){L=null}var $=function(){throw new P},G=L?function(){try{return $}catch(t){try{return L(arguments,"callee").get}catch(e){return $}}}():$,z="function"==typeof _&&"function"==typeof Symbol&&"symbol"==typeof _("foo")&&"symbol"==typeof Symbol("bar")&&function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var o=Object.getOwnPropertySymbols(t);if(1!==o.length||o[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(t,e);if(42!==n.value||!0!==n.enumerable)return!1}return!0}(),q={__proto__:x}.foo===x.foo&&!({__proto__:null}instanceof k),V=Object.getPrototypeOf||(q?function(t){return t.__proto__}:null),K={},J="undefined"!=typeof Uint8Array&&V?V(Uint8Array):void 0,Q={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?void 0:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"%ArrayIteratorPrototype%":z&&V?V([][Symbol.iterator]()):void 0,"%AsyncFromSyncIteratorPrototype%":void 0,"%AsyncFunction%":K,"%AsyncGenerator%":K,"%AsyncGeneratorFunction%":K,"%AsyncIteratorPrototype%":K,"%Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"%BigInt%":"undefined"==typeof BigInt?void 0:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?void 0:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?void 0:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?void 0:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":w,"%eval%":eval,"%EvalError%":S,"%Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?void 0:FinalizationRegistry,"%Function%":B,"%GeneratorFunction%":K,"%Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":z&&V?V(V([][Symbol.iterator]())):void 0,"%JSON%":"object"==typeof JSON?JSON:void 0,"%Map%":"undefined"==typeof Map?void 0:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&z&&V?V((new Map)[Symbol.iterator]()):void 0,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?void 0:Promise,"%Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"%RangeError%":A,"%ReferenceError%":j,"%Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?void 0:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&z&&V?V((new Set)[Symbol.iterator]()):void 0,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":z&&V?V(""[Symbol.iterator]()):void 0,"%Symbol%":z?Symbol:void 0,"%SyntaxError%":O,"%ThrowTypeError%":G,"%TypedArray%":J,"%TypeError%":P,"%Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"%URIError%":E,"%WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?void 0:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet};if(V)try{null.error}catch(Ye){var X=V(V(Ye));Q["%Error.prototype%"]=X}var Y={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Z=T.call(Function.call,Array.prototype.concat),tt=T.call(Function.apply,Array.prototype.splice),et=T.call(Function.call,String.prototype.replace),rt=T.call(Function.call,String.prototype.slice),ot=T.call(Function.call,RegExp.prototype.exec),nt=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,it=/\\(\\)?/g,at=function(t){var e=rt(t,0,1),r=rt(t,-1);if("%"===e&&"%"!==r)throw new O("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new O("invalid intrinsic syntax, expected opening `%`");var o=[];return et(t,nt,(function(t,e,r,n){o[o.length]=r?et(n,it,"$1"):e||t})),o},pt=function(t,e){var r,o=t;if(W(Y,o)&&(o="%"+(r=Y[o])[0]+"%"),W(Q,o)){var n=Q[o];if(n===K&&(n=function t(e){var r;if("%AsyncFunction%"===e)r=H("async function () {}");else if("%GeneratorFunction%"===e)r=H("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=H("async function* () {}");else if("%AsyncGenerator%"===e){var o=t("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===e){var n=t("%AsyncGenerator%");n&&V&&(r=V(n.prototype))}return Q[e]=r,r}(o)),void 0===n&&!e)throw new P("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new O("intrinsic "+t+" does not exist!")},ut=function(t,e){if("string"!=typeof t||0===t.length)throw new P("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new P('"allowMissing" argument must be a boolean');if(null===ot(/^%?[^%]*%?$/,t))throw new O("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=at(t),o=r.length>0?r[0]:"",n=pt("%"+o+"%",e),i=n.name,a=n.value,p=!1,u=n.alias;u&&(o=u[0],tt(r,Z([0,1],u)));for(var l=1,c=!0;l<r.length;l+=1){var f=r[l],y=rt(f,0,1),s=rt(f,-1);if(('"'===y||"'"===y||"`"===y||'"'===s||"'"===s||"`"===s)&&y!==s)throw new O("property names with quotes must have matching quotes");if("constructor"!==f&&c||(p=!0),W(Q,i="%"+(o+="."+f)+"%"))a=Q[i];else if(null!=a){if(!(f in a)){if(!e)throw new P("base intrinsic for "+t+" exists, but the property is not available.");return}if(L&&l+1>=r.length){var d=L(a,f);a=(c=!!d)&&"get"in d&&!("originalValue"in d.get)?d.get:a[f]}else c=W(a,f),a=a[f];c&&!p&&(Q[i]=a)}}return a},lt=ut("%Object.defineProperty%",!0)||!1;if(lt)try{lt({},"a",{value:1})}catch(Ye){lt=!1}var ct=lt,ft=ut("%Object.getOwnPropertyDescriptor%",!0);if(ft)try{ft([],"length")}catch(Ye){ft=null}var yt=ft,st=function(t,e,r){if(!t||"object"!=typeof t&&"function"!=typeof t)throw new P("`obj` must be an object or a function`");if("string"!=typeof e&&"symbol"!=typeof e)throw new P("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new P("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new P("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new P("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new P("`loose`, if provided, must be a boolean");var o=arguments.length>3?arguments[3]:null,n=arguments.length>4?arguments[4]:null,i=arguments.length>5?arguments[5]:null,a=arguments.length>6&&arguments[6],p=!!yt&&yt(t,e);if(ct)ct(t,e,{configurable:null===i&&p?p.configurable:!i,enumerable:null===o&&p?p.enumerable:!o,value:r,writable:null===n&&p?p.writable:!n});else{if(!a&&(o||n||i))throw new O("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");t[e]=r}},dt=function(){return!!ct};dt.hasArrayLengthDefineBug=function(){if(!ct)return null;try{return 1!==ct([],"length",{value:1}).length}catch(Ye){return!0}};var ht,gt=dt(),bt=ut("%Math.floor%"),vt=function(t,e){if("function"!=typeof t)throw new P("`fn` is not a function");if("number"!=typeof e||e<0||e>4294967295||bt(e)!==e)throw new P("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],o=!0,n=!0;if("length"in t&&yt){var i=yt(t,"length");i&&!i.configurable&&(o=!1),i&&!i.writable&&(n=!1)}return(o||n||!r)&&(gt?st(t,"length",e,!0,!0):st(t,"length",e)),t},mt=v((function(t){var e=ut("%Function.prototype.apply%"),r=ut("%Function.prototype.call%"),o=ut("%Reflect.apply%",!0)||T.call(r,e),n=ut("%Math.max%");t.exports=function(t){if("function"!=typeof t)throw new P("a function is required");var e=o(T,r,arguments);return vt(e,1+n(0,t.length-(arguments.length-1)),!0)};var i=function(){return o(T,e,arguments)};ct?ct(t.exports,"apply",{value:i}):t.exports.apply=i})),wt=(mt.apply,mt(ut("String.prototype.indexOf"))),St=function(t,e){var r=ut(t,!!e);return"function"==typeof r&&wt(t,".prototype.")>-1?mt(r):r},At=(ht=Object.freeze({__proto__:null,default:{}}))&&ht.default||ht,jt="function"==typeof Map&&Map.prototype,Ot=Object.getOwnPropertyDescriptor&&jt?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Pt=jt&&Ot&&"function"==typeof Ot.get?Ot.get:null,Et=jt&&Map.prototype.forEach,_t="function"==typeof Set&&Set.prototype,xt=Object.getOwnPropertyDescriptor&&_t?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,kt=_t&&xt&&"function"==typeof xt.get?xt.get:null,It=_t&&Set.prototype.forEach,Ft="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,Rt="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,Dt="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Ut=Boolean.prototype.valueOf,Mt=Object.prototype.toString,Tt=Function.prototype.toString,Nt=String.prototype.match,Ct=String.prototype.slice,Wt=String.prototype.replace,Bt=String.prototype.toUpperCase,Ht=String.prototype.toLowerCase,Lt=RegExp.prototype.test,$t=Array.prototype.concat,Gt=Array.prototype.join,zt=Array.prototype.slice,qt=Math.floor,Vt="function"==typeof BigInt?BigInt.prototype.valueOf:null,Kt=Object.getOwnPropertySymbols,Jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,Qt="function"==typeof Symbol&&"object"==typeof Symbol.iterator,Xt="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,Yt=Object.prototype.propertyIsEnumerable,Zt=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function te(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||Lt.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-qt(-t):qt(t);if(o!==t){var n=String(o),i=Ct.call(e,n.length+1);return Wt.call(n,r,"$&_")+"."+Wt.call(Wt.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Wt.call(e,r,"$&_")}var ee=At.custom,re=ue(ee)?ee:null,oe=function t(e,r,o,n){var i=r||{};if(ce(i,"quoteStyle")&&"single"!==i.quoteStyle&&"double"!==i.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(ce(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var a=!ce(i,"customInspect")||i.customInspect;if("boolean"!=typeof a&&"symbol"!==a)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(ce(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(ce(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var p=i.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return function t(e,r){if(e.length>r.maxStringLength){var o=e.length-r.maxStringLength,n="... "+o+" more character"+(o>1?"s":"");return t(Ct.call(e,0,r.maxStringLength),r)+n}return ne(Wt.call(Wt.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,se),"single",r)}(e,i);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var u=String(e);return p?te(e,u):u}if("bigint"==typeof e){var l=String(e)+"n";return p?te(e,l):l}var c=void 0===i.depth?5:i.depth;if(void 0===o&&(o=0),o>=c&&c>0&&"object"==typeof e)return ae(e)?"[Array]":"[Object]";var f=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;r=Gt.call(Array(t.indent+1)," ")}return{base:r,prev:Gt.call(Array(e+1),r)}}(i,o);if(void 0===n)n=[];else if(ye(n,e)>=0)return"[Circular]";function y(e,r,a){if(r&&(n=zt.call(n)).push(r),a){var p={depth:i.depth};return ce(i,"quoteStyle")&&(p.quoteStyle=i.quoteStyle),t(e,p,o+1,n)}return t(e,i,o+1,n)}if("function"==typeof e&&!pe(e)){var s=function(t){if(t.name)return t.name;var e=Nt.call(Tt.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}(e),d=ve(e,y);return"[Function"+(s?": "+s:" (anonymous)")+"]"+(d.length>0?" { "+Gt.call(d,", ")+" }":"")}if(ue(e)){var h=Qt?Wt.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):Jt.call(e);return"object"!=typeof e||Qt?h:de(h)}if(function(t){return!(!t||"object"!=typeof t)&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)}(e)){for(var g="<"+Ht.call(String(e.nodeName)),v=e.attributes||[],m=0;m<v.length;m++)g+=" "+v[m].name+"="+ne(ie(v[m].value),"double",i);return g+=">",e.childNodes&&e.childNodes.length&&(g+="..."),g+"</"+Ht.call(String(e.nodeName))+">"}if(ae(e)){if(0===e.length)return"[]";var w=ve(e,y);return f&&!function(t){for(var e=0;e<t.length;e++)if(ye(t[e],"\n")>=0)return!1;return!0}(w)?"["+be(w,f)+"]":"[ "+Gt.call(w,", ")+" ]"}if(function(t){return!("[object Error]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}(e)){var S=ve(e,y);return"cause"in Error.prototype||!("cause"in e)||Yt.call(e,"cause")?0===S.length?"["+String(e)+"]":"{ ["+String(e)+"] "+Gt.call(S,", ")+" }":"{ ["+String(e)+"] "+Gt.call($t.call("[cause]: "+y(e.cause),S),", ")+" }"}if("object"==typeof e&&a){if(re&&"function"==typeof e[re]&&At)return At(e,{depth:c-o});if("symbol"!==a&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!Pt||!t||"object"!=typeof t)return!1;try{Pt.call(t);try{kt.call(t)}catch(g){return!0}return t instanceof Map}catch(Ye){}return!1}(e)){var A=[];return Et&&Et.call(e,(function(t,r){A.push(y(r,e,!0)+" => "+y(t,e))})),ge("Map",Pt.call(e),A,f)}if(function(t){if(!kt||!t||"object"!=typeof t)return!1;try{kt.call(t);try{Pt.call(t)}catch(e){return!0}return t instanceof Set}catch(Ye){}return!1}(e)){var j=[];return It&&It.call(e,(function(t){j.push(y(t,e))})),ge("Set",kt.call(e),j,f)}if(function(t){if(!Ft||!t||"object"!=typeof t)return!1;try{Ft.call(t,Ft);try{Rt.call(t,Rt)}catch(g){return!0}return t instanceof WeakMap}catch(Ye){}return!1}(e))return he("WeakMap");if(function(t){if(!Rt||!t||"object"!=typeof t)return!1;try{Rt.call(t,Rt);try{Ft.call(t,Ft)}catch(g){return!0}return t instanceof WeakSet}catch(Ye){}return!1}(e))return he("WeakSet");if(function(t){if(!Dt||!t||"object"!=typeof t)return!1;try{return Dt.call(t),!0}catch(Ye){}return!1}(e))return he("WeakRef");if(function(t){return!("[object Number]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}(e))return de(y(Number(e)));if(function(t){if(!t||"object"!=typeof t||!Vt)return!1;try{return Vt.call(t),!0}catch(Ye){}return!1}(e))return de(y(Vt.call(e)));if(function(t){return!("[object Boolean]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}(e))return de(Ut.call(e));if(function(t){return!("[object String]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}(e))return de(y(String(e)));if("undefined"!=typeof window&&e===window)return"{ [object Window] }";if(e===b)return"{ [object globalThis] }";if(!function(t){return!("[object Date]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}(e)&&!pe(e)){var O=ve(e,y),P=Zt?Zt(e)===Object.prototype:e instanceof Object||e.constructor===Object,E=e instanceof Object?"":"null prototype",_=!P&&Xt&&Object(e)===e&&Xt in e?Ct.call(fe(e),8,-1):E?"Object":"",x=(P||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(_||E?"["+Gt.call($t.call([],_||[],E||[]),": ")+"] ":"");return 0===O.length?x+"{}":f?x+"{"+be(O,f)+"}":x+"{ "+Gt.call(O,", ")+" }"}return String(e)};function ne(t,e,r){var o="double"===(r.quoteStyle||e)?'"':"'";return o+t+o}function ie(t){return Wt.call(String(t),/"/g,"&quot;")}function ae(t){return!("[object Array]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}function pe(t){return!("[object RegExp]"!==fe(t)||Xt&&"object"==typeof t&&Xt in t)}function ue(t){if(Qt)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!Jt)return!1;try{return Jt.call(t),!0}catch(Ye){}return!1}var le=Object.prototype.hasOwnProperty||function(t){return t in this};function ce(t,e){return le.call(t,e)}function fe(t){return Mt.call(t)}function ye(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function se(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+Bt.call(e.toString(16))}function de(t){return"Object("+t+")"}function he(t){return t+" { ? }"}function ge(t,e,r,o){return t+" ("+e+") {"+(o?be(r,o):Gt.call(r,", "))+"}"}function be(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+Gt.call(t,","+r)+"\n"+e.prev}function ve(t,e){var r=ae(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=ce(t,n)?e(t[n],t):""}var i,a="function"==typeof Kt?Kt(t):[];if(Qt){i={};for(var p=0;p<a.length;p++)i["$"+a[p]]=a[p]}for(var u in t)ce(t,u)&&(r&&String(Number(u))===u&&u<t.length||Qt&&i["$"+u]instanceof Symbol||(Lt.call(/[^\w$]/,u)?o.push(e(u,t)+": "+e(t[u],t)):o.push(u+": "+e(t[u],t))));if("function"==typeof Kt)for(var l=0;l<a.length;l++)Yt.call(t,a[l])&&o.push("["+e(a[l])+"]: "+e(t[a[l]],t));return o}var me=ut("%WeakMap%",!0),we=ut("%Map%",!0),Se=St("WeakMap.prototype.get",!0),Ae=St("WeakMap.prototype.set",!0),je=St("WeakMap.prototype.has",!0),Oe=St("Map.prototype.get",!0),Pe=St("Map.prototype.set",!0),Ee=St("Map.prototype.has",!0),_e=function(t,e){for(var r,o=t;null!==(r=o.next);o=r)if(r.key===e)return o.next=r.next,r.next=t.next,t.next=r,r},xe=function(){var t,e,r,o={assert:function(t){if(!o.has(t))throw new P("Side channel does not contain "+oe(t))},get:function(o){if(me&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Se(t,o)}else if(we){if(e)return Oe(e,o)}else if(r)return function(t,e){var r=_e(t,e);return r&&r.value}(r,o)},has:function(o){if(me&&o&&("object"==typeof o||"function"==typeof o)){if(t)return je(t,o)}else if(we){if(e)return Ee(e,o)}else if(r)return function(t,e){return!!_e(t,e)}(r,o);return!1},set:function(o,n){me&&o&&("object"==typeof o||"function"==typeof o)?(t||(t=new me),Ae(t,o,n)):we?(e||(e=new we),Pe(e,o,n)):(r||(r={key:{},next:null}),function(t,e,r){var o=_e(t,e);o?o.value=r:t.next={key:e,next:t.next,value:r}}(r,o,n))}};return o},ke=String.prototype.replace,Ie=/%20/g,Fe="RFC3986",Re={default:Fe,formatters:{RFC1738:function(t){return ke.call(t,Ie,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:Fe},De=Object.prototype.hasOwnProperty,Ue=Array.isArray,Me=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),Te=function(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r},Ne={arrayToObject:Te,assign:function(t,e){return Object.keys(e).reduce((function(t,r){return t[r]=e[r],t}),t)},combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],i=n.obj[n.prop],a=Object.keys(i),p=0;p<a.length;++p){var u=a[p],l=i[u];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(e.push({obj:i,prop:u}),r.push(l))}return function(t){for(;t.length>1;){var e=t.pop(),r=e.obj[e.prop];if(Ue(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);e.obj[e.prop]=o}}}(e),t},decode:function(t,e,r){var o=t.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(Ye){return o}},encode:function(t,e,r,o,n){if(0===t.length)return t;var i=t;if("symbol"==typeof t?i=Symbol.prototype.toString.call(t):"string"!=typeof t&&(i=String(t)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var a="",p=0;p<i.length;p+=1024){for(var u=i.length>=1024?i.slice(p,p+1024):i,l=[],c=0;c<u.length;++c){var f=u.charCodeAt(c);45===f||46===f||95===f||126===f||f>=48&&f<=57||f>=65&&f<=90||f>=97&&f<=122||n===Re.RFC1738&&(40===f||41===f)?l[l.length]=u.charAt(c):f<128?l[l.length]=Me[f]:f<2048?l[l.length]=Me[192|f>>6]+Me[128|63&f]:f<55296||f>=57344?l[l.length]=Me[224|f>>12]+Me[128|f>>6&63]+Me[128|63&f]:(c+=1,f=65536+((1023&f)<<10|1023&u.charCodeAt(c)),l[l.length]=Me[240|f>>18]+Me[128|f>>12&63]+Me[128|f>>6&63]+Me[128|63&f])}a+=l.join("")}return a},isBuffer:function(t){return!(!t||"object"!=typeof t)&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(Ue(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function t(e,r,o){if(!r)return e;if("object"!=typeof r){if(Ue(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(o&&(o.plainObjects||o.allowPrototypes)||!De.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);var n=e;return Ue(e)&&!Ue(r)&&(n=Te(e,o)),Ue(e)&&Ue(r)?(r.forEach((function(r,n){if(De.call(e,n)){var i=e[n];i&&"object"==typeof i&&r&&"object"==typeof r?e[n]=t(i,r,o):e.push(r)}else e[n]=r})),e):Object.keys(r).reduce((function(e,n){var i=r[n];return De.call(e,n)?e[n]=t(e[n],i,o):e[n]=i,e}),n)}},Ce=Object.prototype.hasOwnProperty,We={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},Be=Array.isArray,He=Array.prototype.push,Le=function(t,e){He.apply(t,Be(e)?e:[e])},$e=Date.prototype.toISOString,Ge=Re.default,ze={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Ne.encode,encodeValuesOnly:!1,format:Ge,formatter:Re.formatters[Ge],indices:!1,serializeDate:function(t){return $e.call(t)},skipNulls:!1,strictNullHandling:!1},qe={},Ve=function t(e,r,o,n,i,a,p,u,l,c,f,y,s,d,h,g,b,v){for(var m,w=e,S=v,A=0,j=!1;void 0!==(S=S.get(qe))&&!j;){var O=S.get(e);if(A+=1,void 0!==O){if(O===A)throw new RangeError("Cyclic object value");j=!0}void 0===S.get(qe)&&(A=0)}if("function"==typeof c?w=c(r,w):w instanceof Date?w=s(w):"comma"===o&&Be(w)&&(w=Ne.maybeMap(w,(function(t){return t instanceof Date?s(t):t}))),null===w){if(a)return l&&!g?l(r,ze.encoder,b,"key",d):r;w=""}if("string"==typeof(m=w)||"number"==typeof m||"boolean"==typeof m||"symbol"==typeof m||"bigint"==typeof m||Ne.isBuffer(w))return l?[h(g?r:l(r,ze.encoder,b,"key",d))+"="+h(l(w,ze.encoder,b,"value",d))]:[h(r)+"="+h(String(w))];var P,E=[];if(void 0===w)return E;if("comma"===o&&Be(w))g&&l&&(w=Ne.maybeMap(w,l)),P=[{value:w.length>0?w.join(",")||null:void 0}];else if(Be(c))P=c;else{var _=Object.keys(w);P=f?_.sort(f):_}var x=u?r.replace(/\./g,"%2E"):r,k=n&&Be(w)&&1===w.length?x+"[]":x;if(i&&Be(w)&&0===w.length)return k+"[]";for(var I=0;I<P.length;++I){var F=P[I],R="object"==typeof F&&void 0!==F.value?F.value:w[F];if(!p||null!==R){var D=y&&u?F.replace(/\./g,"%2E"):F,U=Be(w)?"function"==typeof o?o(k,D):k:k+(y?"."+D:"["+D+"]");v.set(e,A);var M=xe();M.set(qe,v),Le(E,t(R,U,o,n,i,a,p,u,"comma"===o&&g&&Be(w)?null:l,c,f,y,s,d,h,g,b,M))}}return E},Ke=(Object.prototype.hasOwnProperty,Array.isArray,function(t,e){var r,o=t,n=function(t){if(!t)return ze;if(void 0!==t.allowEmptyArrays&&"boolean"!=typeof t.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==t.encodeDotInKeys&&"boolean"!=typeof t.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||ze.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=Re.default;if(void 0!==t.format){if(!Ce.call(Re.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o,n=Re.formatters[r],i=ze.filter;if(("function"==typeof t.filter||Be(t.filter))&&(i=t.filter),o=t.arrayFormat in We?t.arrayFormat:"indices"in t?t.indices?"indices":"repeat":ze.arrayFormat,"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a=void 0===t.allowDots?!0===t.encodeDotInKeys||ze.allowDots:!!t.allowDots;return{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:ze.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof t.allowEmptyArrays?!!t.allowEmptyArrays:ze.allowEmptyArrays,arrayFormat:o,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:ze.charsetSentinel,commaRoundTrip:t.commaRoundTrip,delimiter:void 0===t.delimiter?ze.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:ze.encode,encodeDotInKeys:"boolean"==typeof t.encodeDotInKeys?t.encodeDotInKeys:ze.encodeDotInKeys,encoder:"function"==typeof t.encoder?t.encoder:ze.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:ze.encodeValuesOnly,filter:i,format:r,formatter:n,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:ze.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:ze.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:ze.strictNullHandling}}(e);"function"==typeof n.filter?o=(0,n.filter)("",o):Be(n.filter)&&(r=n.filter);var i=[];if("object"!=typeof o||null===o)return"";var a=We[n.arrayFormat],p="comma"===a&&n.commaRoundTrip;r||(r=Object.keys(o)),n.sort&&r.sort(n.sort);for(var u=xe(),l=0;l<r.length;++l){var c=r[l];n.skipNulls&&null===o[c]||Le(i,Ve(o[c],c,a,p,n.allowEmptyArrays,n.strictNullHandling,n.skipNulls,n.encodeDotInKeys,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,u))}var f=i.join(n.delimiter),y=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),f.length>0?y+f:""});function Je(t){this.webAuth=t,this._current_popup=null,this.options=null}function Qe(t){this.webAuth=t}function Xe(){this.webAuth=null,this.version=t,this.extensibilityPoints=["popup.authorize","popup.getPopupHandler"]}return Je.prototype.preload=function(t){var e=this,r=g.getWindow(),o=t.url||"about:blank",n=t.popupOptions||{};n.location="yes",delete n.width,delete n.height;var i=Ke(n,{encode:!1,delimiter:","});return this._current_popup&&!this._current_popup.closed||(this._current_popup=r.open(o,"_blank",i),this._current_popup.kill=function(t){e._current_popup.success=t,this.close(),e._current_popup=null}),this._current_popup},Je.prototype.load=function(t,e,r,o){var n=this;this.url=t,this.options=r,this._current_popup?this._current_popup.location.href=t:(r.url=t,this.preload(r)),this.transientErrorHandler=function(t){n.errorHandler(t,o)},this.transientStartHandler=function(t){n.startHandler(t,o)},this.transientExitHandler=function(){n.exitHandler(o)},this._current_popup.addEventListener("loaderror",this.transientErrorHandler),this._current_popup.addEventListener("loadstart",this.transientStartHandler),this._current_popup.addEventListener("exit",this.transientExitHandler)},Je.prototype.errorHandler=function(t,e){this._current_popup&&(this._current_popup.kill(!0),e({error:"window_error",errorDescription:t.message}))},Je.prototype.unhook=function(){this._current_popup.removeEventListener("loaderror",this.transientErrorHandler),this._current_popup.removeEventListener("loadstart",this.transientStartHandler),this._current_popup.removeEventListener("exit",this.transientExitHandler)},Je.prototype.exitHandler=function(t){this._current_popup&&(this.unhook(),this._current_popup.success||t({error:"window_closed",errorDescription:"Browser window closed"}))},Je.prototype.startHandler=function(t,e){var r=this;if(this._current_popup){var o=m("https:",this.webAuth.baseOptions.domain,"/mobile");if(!t.url||0===t.url.indexOf(o+"#")){var n=t.url.split("#");if(1!==n.length){var i={hash:n.pop()};this.options.nonce&&(i.nonce=this.options.nonce),this.webAuth.parseHash(i,(function(t,o){(t||o)&&(r._current_popup.kill(!0),e(t,o))}))}}}},Qe.prototype.processParams=function(t){return t.redirectUri=m("https://"+t.domain,"mobile"),delete t.owp,t},Qe.prototype.getPopupHandler=function(){return new Je(this.webAuth)},Xe.prototype.setWebAuth=function(t){this.webAuth=t},Xe.prototype.supports=function(t){var e=g.getWindow();return(!!e.cordova||!!e.electron)&&this.extensibilityPoints.indexOf(t)>-1},Xe.prototype.init=function(){return new Qe(this.webAuth)},Xe}))}}]);