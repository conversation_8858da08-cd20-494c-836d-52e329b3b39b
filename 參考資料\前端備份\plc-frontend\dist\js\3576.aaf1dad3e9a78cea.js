"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[1143,3003,3576],{13576:function(e,a,t){t.r(a),t.d(a,{default:function(){return Q}});var l=t(20641),n=t(72644),o=t(9322);const i={key:1},r={key:2},s={key:0},u={class:"ninjadash-chart-container"};function d(e,a,t,d,c,p){const g=(0,l.g2)("sdPageHeader"),m=(0,l.g2)("AddModal"),h=(0,l.g2)("DetailModal"),b=(0,l.g2)("sdButton"),f=(0,l.g2)("a-spin"),y=(0,l.g2)("unicon"),v=(0,l.g2)("a-button"),k=(0,l.g2)("a-space"),C=(0,l.g2)("a-row"),F=(0,l.g2)("Chart"),S=(0,l.g2)("a-col"),_=(0,l.g2)("sdCards"),w=(0,l.g2)("CardWrap"),x=(0,l.g2)("Main");return(0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(g,{title:"可靠度分析",class:"ninjadash-page-header-main",routes:[{breadcrumbName:"警報系統"},{breadcrumbName:"可靠度分析"}]}),(0,l.bF)(m,{modal:e.modal,formState:e.formState,closeModal:e.closeAddModal,onSetTags:e.setTags,onSetGroups:e.setGroups,onChangeSearchType:e.changeSearchType,onSubmit:e.submitGroup},null,8,["modal","formState","closeModal","onSetTags","onSetGroups","onChangeSearchType","onSubmit"]),(0,l.bF)(h,{modal:e.detailModal,title:e.detailModalTitle,closeModal:e.closeDetailModal},null,8,["modal","title","closeModal"]),(0,l.bF)(x,null,{default:(0,l.k6)((()=>[e.permission.create?((0,l.uX)(),(0,l.Wv)(b,{key:0,class:"act-btn",type:"primary",style:{"margin-bottom":"1rem"},onClick:e.openAddModal},{default:(0,l.k6)((()=>[(0,l.eW)(" 新增 ")])),_:1},8,["onClick"])):(0,l.Q3)("",!0),e.loading?((0,l.uX)(),(0,l.CE)("div",i,[(0,l.bF)(f)])):(0,l.Q3)("",!0),e.loading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",r,[0===e.groups.length?((0,l.uX)(),(0,l.CE)("div",s,"尚未建立任何群組")):(0,l.Q3)("",!0),0!==e.groups.length?((0,l.uX)(),(0,l.Wv)(C,{key:1,gutter:[10,10]},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.groups,(a=>((0,l.uX)(),(0,l.Wv)(S,{key:a.id,lg:6,md:12,xs:24},{default:(0,l.k6)((()=>[(0,l.bF)(w,null,{default:(0,l.k6)((()=>[(0,l.bF)(_,{title:a.title},{default:(0,l.k6)((()=>[(0,l.bF)(C,{align:"space-between",style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.Lk)("h3",null,(0,n.v_)(a.Name),1),(0,l.bF)(k,null,{default:(0,l.k6)((()=>[e.permission.delete?((0,l.uX)(),(0,l.Wv)(v,{key:0,ghost:"",type:"danger",class:"del-btn",onClick:(0,o.D$)((t=>e.deleteGroup(a.Id)),["prevent"])},{default:(0,l.k6)((()=>[(0,l.bF)(y,{name:"trash"})])),_:2},1032,["onClick"])):(0,l.Q3)("",!0),e.permission.update?((0,l.uX)(),(0,l.Wv)(v,{key:1,ghost:"",type:"primary",onClick:(0,o.D$)((t=>e.openEditModal(a)),["prevent"])},{default:(0,l.k6)((()=>[(0,l.bF)(y,{name:"setting"})])),_:2},1032,["onClick"])):(0,l.Q3)("",!0)])),_:2},1024)])),_:2},1024),(0,l.Lk)("p",null,"上次重置時間: "+(0,n.v_)(a.resetTime),1),(0,l.Lk)("div",u,[e.showChart?((0,l.uX)(),(0,l.Wv)(F,{key:0,id:`pieChart-${a.Id}`,type:"pie",className:"piewrap",labels:e.label,options:e.options,datasets:a.datasets},null,8,["id","labels","options","datasets"])):(0,l.Q3)("",!0)]),(0,l.bF)(C,{style:{"margin-top":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(S,{class:"bordered-col",span:12},{default:(0,l.k6)((()=>[(0,l.bF)(C,{align:"space-between"},{default:(0,l.k6)((()=>[(0,l.bF)(S,null,{default:(0,l.k6)((()=>[(0,l.eW)("故障次數")])),_:1}),(0,l.bF)(S,{class:"action",onClick:t=>e.resetCount(a.Id)},{default:(0,l.k6)((()=>[(0,l.eW)("重置")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024),(0,l.bF)(S,{class:"bordered-col",style:{"text-align":"right"},span:12},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(a.TotalFaultCount),1)])),_:2},1024),(0,l.bF)(S,{class:"bordered-col",span:12},{default:(0,l.k6)((()=>[(0,l.bF)(C,{align:"space-between"},{default:(0,l.k6)((()=>[(0,l.bF)(S,null,{default:(0,l.k6)((()=>[(0,l.eW)("總次數")])),_:1})])),_:1})])),_:1}),(0,l.bF)(S,{class:"bordered-col",style:{"text-align":"right"},span:12},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(a.FaultToleranceCount),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["title"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(0,l.Q3)("",!0)]))])),_:1})])}t(75126);var c=t(56427),p=(t(1532),t(30995)),g=(t(18111),t(61701),t(79841)),m=t(79570),h=t(40834),b=t(94734),f=t(7153);function y(e,a,t,n,i,r){const s=(0,l.g2)("a-input"),u=(0,l.g2)("a-form-item"),d=(0,l.g2)("GroupFilter"),c=(0,l.g2)("TagFilter"),p=(0,l.g2)("a-spin"),g=(0,l.g2)("a-button"),m=(0,l.g2)("a-col"),h=(0,l.g2)("a-row"),b=(0,l.g2)("a-form"),f=(0,l.g2)("sdModal");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(f,{key:0,title:e.formState.title,visible:e.modal,onCancel:e.closeModal},{default:(0,l.k6)((()=>[(0,l.bF)(b,{ref:"formRef",model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.rules,labelAlign:"left",name:"type",onFinish:e.submit},{default:(0,l.k6)((()=>[(0,l.bF)(u,{label:"圖表名稱",name:"name"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.formState.name,"onUpdate:value":a[0]||(a[0]=a=>e.formState.name=a),style:{height:"40px"}},null,8,["value"])])),_:1}),(0,l.bF)(u,{label:"總次數",name:"count"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.formState.count,"onUpdate:value":a[1]||(a[1]=a=>e.formState.count=a),style:{height:"40px"}},null,8,["value"])])),_:1}),(0,l.bF)(u,{label:"選擇群組"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{selectedGroups:e.formState.groups,onSetGroups:e.setGroups,style:{height:"40px"}},null,8,["selectedGroups","onSetGroups"])])),_:1}),(0,l.bF)(u,{label:"選擇測點"},{default:(0,l.k6)((()=>[(0,l.bF)(c,{selectedTags:e.formState.tags,onSetTags:e.setTags,style:{height:"40px"}},null,8,["selectedTags","onSetTags"])])),_:1}),(0,l.bF)(h,{gutter:10,justify:"center"},{default:(0,l.k6)((()=>[(0,l.bF)(m,null,{default:(0,l.k6)((()=>[(0,l.bF)(g,{type:"primary","html-type":"submit",style:{height:"40px"}},{default:(0,l.k6)((()=>[(0,l.eW)(" 儲存 "),(0,l.bo)((0,l.bF)(p,{size:"small"},null,512),[[o.aG,e.loading]])])),_:1})])),_:1}),(0,l.bF)(m,null,{default:(0,l.k6)((()=>[(0,l.bF)(g,{type:"primary",ghost:"",style:{height:"40px"},onClick:(0,o.D$)(e.closeModal,["prevent"])},{default:(0,l.k6)((()=>[(0,l.eW)("取消")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0)])}var v=t(82046),k=t(59835),C=(0,l.pM)({components:{LevelSelect:b.A,TagFilter:v.A,GroupFilter:k.A},props:{formState:{required:!0,type:Object},modal:{type:Boolean,default:!1},closeModal:{type:Function,required:!0}},setup(e,{emit:a}){const{state:t}=(0,h.Pj)(),n=(0,l.EW)((()=>t.alarm.loading)),o=(0,g.KR)(),i={lg:8,md:9,xs:24},r={lg:16,md:15,xs:24},s={name:[{required:!0,trigger:"blur",message:"請輸入"}],count:[{required:!0,message:"請輸入",trigger:"blur"},{trigger:"blur",pattern:/^\d+$/,message:"請輸入數字"}]},u=[{id:"1",name:"依測點"},{id:"2",name:"依群組"}],d=e=>{a("setTags",e.map((e=>e.id)))},c=e=>{a("setGroups",e.map((e=>e.id)))},p=()=>{a("submit")},m=()=>{a("changeSearchType")};return{loading:n,formRef:o,labelCol:i,wrapperCol:r,rules:s,searchTypeOptions:u,setTags:d,setGroups:c,submit:p,changeSearchType:m}}}),F=t(66262);const S=(0,F.A)(C,[["render",y]]);var _=S;const w={style:{display:"flex","justify-content":"end","margin-bottom":"1rem"}},x={class:"table-data-view table-responsive"};function T(e,a,t,n,o,i){const r=(0,l.g2)("a-spin"),s=(0,l.g2)("unicon"),u=(0,l.g2)("a-input"),d=(0,l.g2)("a-table"),c=(0,l.g2)("sdCards"),p=(0,l.g2)("sdModal");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(p,{key:0,title:e.title,visible:e.modal,onCancel:e.closeModal,width:800},{default:(0,l.k6)((()=>[(0,l.bF)(c,null,{default:(0,l.k6)((()=>[e.loading?((0,l.uX)(),(0,l.Wv)(r,{key:0})):(0,l.Q3)("",!0),(0,l.Lk)("div",w,[(0,l.bF)(u,{style:{width:"300px"},onChange:e.search,size:"default",placeholder:"搜尋"},{prefix:(0,l.k6)((()=>[(0,l.bF)(s,{name:"search"})])),_:1},8,["onChange"])]),(0,l.Lk)("div",x,[(0,l.bF)(d,{columns:e.columns,"data-source":e.tableData},{expandedRowRender:(0,l.k6)((({record:a})=>[(0,l.bF)(d,{columns:e.innerColumns,"data-source":a.details,pagination:!0},null,8,["columns","data-source"])])),_:1},8,["columns","data-source"])])])),_:1})])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0)])}var A=t(25117),W=t(74353),M=t.n(W),O=t(82077),D=(0,l.pM)({props:{modal:{type:Boolean,default:!1},title:{type:String,required:!0},closeModal:{type:Function,required:!0}},components:{ModalTable:A.A},setup(){const{state:e,dispatch:a}=(0,h.Pj)(),t=(0,l.EW)((()=>e.alarm.loading)),n=[{title:"測點名稱",dataIndex:"name",key:"name",fixed:"left"},{title:"描述",dataIndex:"description",key:"description"},{title:"故障次數",dataIndex:"value",key:"value",align:"right"}],o=[{title:"故障時間",dataIndex:"value",key:"value"}],i=(0,l.EW)((()=>e.alarm.reliabilityTableData.map((e=>({...e,key:e.TagName,name:e.TagName,description:(0,O.M)(e.TagId,"Description"),value:e.FaultOccurredTimes&&e.FaultOccurredTimes.length,details:e.FaultOccurredTimes.map((e=>({value:M()(e).format("YYYY-MM-DD HH:mm:ss")})))}))))),r=e=>{a("alarm/filterReliabilityData",e.target.value)};return{loading:t,columns:n,innerColumns:o,tableData:i,search:r}}});const R=(0,F.A)(D,[["render",T]]);var X=R,N=t(95853);const G=N.Ay.div`
    .piewrap{
        max-width:300px;
        max-height:300px;
       
        width:100%;
        margin:auto
    }
    .bordered-col {
        border: 1px solid #e8e8e8; 
        padding: 16px; 
    }
        
    .action{
        color:${({theme:e})=>e["primary-color"]}
        cursor:pointer
    }
    button{
        .unicon svg {
            color: ${({theme:e})=>e["primary-color"]};
            fill: ${({theme:e})=>e["primary-color"]};
        }
       
    }

    .del-btn {
        .unicon svg {
            color: ${({theme:e})=>e["error-color"]};
            fill: ${({theme:e})=>e["error-color"]};
        }
    }
   
`;var E=t(82958),j=(0,l.pM)({components:{Main:m.gZ,LevelSelect:b.A,Chart:f.A,CardWrap:G,AddModal:_,DetailModal:X},setup(){const{permission:e}=(0,E.J)(),{state:a,dispatch:t}=(0,h.Pj)();(0,l.sV)((async()=>{await t("alarm/getReliabilityGroups"),(0,l.dY)((()=>{n.value=!0}))}));const n=(0,g.KR)(!1),o=(0,l.EW)((()=>a.alarm.loading)),i=(0,l.EW)((()=>a.alarm.reliabilityGroup.map((e=>({...e,resetTime:e.FaultToleranceResetTime?M()(e.FaultToleranceResetTime).format("YYYY-MM-DD HH:mm:ss"):"未重置",datasets:[{id:e.Id,name:e.Name,label:"可靠度",data:[e.TotalFaultCount,e.FaultToleranceCount>e.TotalFaultCount?e.FaultToleranceCount-e.TotalFaultCount:0],backgroundColor:["#FF8000","#66B2FF","#5840FF"]}]}))))),r=["警報發生次數","剩餘次數"],s={borderWidth:2,maintainAspectRatio:!0,responsive:!0,scales:{x:{display:!1},y:{display:!1}},plugins:{legend:{display:!1},labels:{display:!1}},animation:{animateScale:!0,animateRotate:!0},onClick:async e=>{const a=e.chart.data.datasets[0];await t("alarm/fetchReliabilityDetail",a.id),w.value=`${a.name} 詳情`,_.value=!0}},u=(0,g.KR)(!1),d=(0,g.Kh)({title:"",id:null,name:null,tags:[],groups:[],count:null}),m=()=>{const e={title:"新增圖表",id:null,name:null,tags:[],groups:[],count:null};Object.assign(d,e),u.value=!0},b=({Id:e,Name:a,TagGroups:t,Tags:l,FaultToleranceCount:n})=>{d.title="編輯圖表",d.id=e,d.name=a,d.tags=l,d.groups=t,d.count=n,u.value=!0},f=()=>{u.value=!1},y=()=>{d.data=[]},v=e=>{d.tags=e},k=e=>{d.groups=e},C=e=>{p.A.confirm({title:"確認重置?",okText:"確認",cancelText:"取消",confirmLoading:o.value,onOk:async()=>{try{await t("alarm/resetReliabiliyCount",e),c.A.success({mesaage:"重置成功"})}catch(a){p.A.error({title:"發生錯誤",content:a.message})}}})},F=async()=>{try{let e;d.id?(await t("alarm/editReliabiliyGroup",(0,g.ux)(d)),e="修改成功"):(await t("alarm/addReliabiliyGroup",(0,g.ux)(d)),e="新增成功"),u.value=!1,c.A.success({message:e})}catch(e){p.A.error({title:"發生錯誤",content:e.message})}},S=e=>{p.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:o.value,onOk:async()=>{try{await t("alarm/deleteReliabiliyGroup",e),c.A.success({message:"刪除成功"})}catch(a){p.A.error({title:"發生錯誤",content:a.message})}}})},_=(0,g.KR)(!1),w=(0,g.KR)(""),x=()=>{_.value=!1};return{permission:e,showChart:n,loading:o,groups:i,label:r,options:s,modal:u,formState:d,openAddModal:m,openEditModal:b,closeAddModal:f,changeSearchType:y,setTags:v,setGroups:k,resetCount:C,submitGroup:F,deleteGroup:S,detailModal:_,detailModalTitle:w,closeDetailModal:x}}});const z=(0,F.A)(j,[["render",d]]);var Q=z},25117:function(e,a,t){t.d(a,{A:function(){return u}});var l=t(20641),n=t(79841),o=t(45118),i=t(18683),r={__name:"ModalTable",props:{loading:{type:Boolean,default:!1},modal:{type:Boolean,default:!1},title:{type:String,required:!0},columns:{type:Array,required:!0},tableData:{type:Array,required:!0},closeModal:{type:Function,required:!0},rowSelection:{type:Boolean,default:!1},onSelectChange:{type:Function,default:()=>{}}},setup(e){const a=e;(0,l.sV)((()=>{t.value=a.tableData}));const t=(0,n.KR)([]),r=e=>{t.value=(0,i.AQ)(a.tableData,e.target.value)};return(a,n)=>{const i=(0,l.g2)("a-spin"),s=(0,l.g2)("sdCards"),u=(0,l.g2)("sdModal");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(u,{key:0,title:e.title,visible:e.modal,onCancel:e.closeModal,width:1600},{default:(0,l.k6)((()=>[(0,l.bF)(s,null,{default:(0,l.k6)((()=>[e.loading?((0,l.uX)(),(0,l.Wv)(i,{key:0})):(0,l.Q3)("",!0),(0,l.bF)(o.A,{filterOption:!0,filterOnchange:!0,tableData:t.value,columns:e.columns,rowSelection:e.rowSelection,handleDataSearch:r,onOnSelectChange:e.onSelectChange},null,8,["tableData","columns","rowSelection","onOnSelectChange"])])),_:1})])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0)])}}};const s=r;var u=s},45118:function(e,a,t){t.d(a,{A:function(){return f}});var l=t(20641),n=t(72644);const o={class:"ninjadash-datatable-filter"},i={key:0,class:"ninjadash-datatable-filter__right"},r={class:"ninjadasj-datatable"};function s(e,a,t,s,u,d){const c=(0,l.g2)("sdButton"),p=(0,l.g2)("a-space"),g=(0,l.g2)("unicon"),m=(0,l.g2)("a-input"),h=(0,l.g2)("a-button"),b=(0,l.g2)("a-col"),f=(0,l.g2)("a-row"),y=(0,l.g2)("a-table"),v=(0,l.g2)("TableWrapper"),k=(0,l.g2)("DataTableStyleWrap");return(0,l.uX)(),(0,l.Wv)(k,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",o,[(0,l.Lk)("div",null,[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[e.addOption?((0,l.uX)(),(0,l.Wv)(c,{key:0,class:"act-btn",type:"primary",onClick:e.handleAdd},{default:(0,l.k6)((()=>[(0,l.eW)(" 新增 ")])),_:1},8,["onClick"])):(0,l.Q3)("",!0),e.backOption?((0,l.uX)(),(0,l.Wv)(c,{key:1,size:"default",outlined:!0,type:"primary",onClick:e.handleBack},{default:(0,l.k6)((()=>[(0,l.eW)(" 回上層 "+(0,n.v_)(e.backTitle),1)])),_:1},8,["onClick"])):(0,l.Q3)("",!0)])),_:1})]),e.filterOption?((0,l.uX)(),(0,l.CE)("div",i,[(0,l.bF)(m,{onChange:e.handleDataSearch,size:"default",placeholder:"搜尋"},{prefix:(0,l.k6)((()=>[(0,l.bF)(g,{name:"search"})])),_:1},8,["onChange"])])):(0,l.Q3)("",!0)]),(0,l.bF)(f,{align:"end"},{default:(0,l.k6)((()=>[e.importOption?((0,l.uX)(),(0,l.Wv)(b,{key:0,style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(h,{type:"primary",ghost:"",onClick:e.handleImport},{default:(0,l.k6)((()=>[(0,l.eW)("匯入")])),_:1},8,["onClick"])])),_:1})):(0,l.Q3)("",!0),e.exportOption?((0,l.uX)(),(0,l.Wv)(b,{key:1,style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(h,{type:"primary",ghost:"",onClick:e.handleExport},{default:(0,l.k6)((()=>[(0,l.eW)("匯出Excel")])),_:1},8,["onClick"])])),_:1})):(0,l.Q3)("",!0)])),_:1}),(0,l.Lk)("div",r,[(0,l.bF)(v,{class:"table-data-view table-responsive"},{default:(0,l.k6)((()=>[e.rowSelection?((0,l.uX)(),(0,l.Wv)(y,{key:0,class:"ant-table-striped","row-selection":e.rowSelections,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},childrenColumnName:e.childrenColumnName,"row-class-name":e.getRowClassName,"data-source":e.tableData,columns:e.columns,onChange:e.changePageSize},null,8,["row-selection","pagination","childrenColumnName","row-class-name","data-source","columns","onChange"])):((0,l.uX)(),(0,l.Wv)(y,{key:1,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},class:"ant-table-striped",childrenColumnName:e.childrenColumnName,"data-source":e.tableData,"row-class-name":e.getRowClassName,columns:e.columns,onChange:e.changePageSize},null,8,["pagination","childrenColumnName","data-source","row-class-name","columns","onChange"]))])),_:1})])])),_:1})}var u=t(79841),d=t(19732),c=t(95853);const p=c.Ay.div`
    .ninjadash-datatable-filter{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 20px 0 25px 0;
        @media only screen and (max-width: 575px){
            flex-wrap: wrap;
        }
        .ninjadash-datatable-filter__left{
            display: inline-flex;
            width: 100%;
            align-items: center;
            .ant-form{
                display: inline-flex;
                width: 100%;
                align-items: center;
            }
            span.label{
                margin-right: 8px;
                color: ${({theme:e})=>e[e.mainContent]["gray-text"]};
            }
            .ninjadash-datatable-filter__input{
                display: flex;
                align-items: center;
                padding-right: 20px;
                .ant-input{
                    height: 40px;
                }
            }
        }
        .ninjadash-datatable-filter__right{
            min-width: 280px;
            @media only screen and (max-width: 575px){
                margin-top: 15px;
            }
            .ant-input-affix-wrapper{
                padding: 7.22px 20px;
                border-radius: 6px;
                .ant-input-prefix{
                    svg{
                        width: 16px;
                        height: 16px;
                        fill: ${({theme:e})=>e[e.mainContent]["light-text"]};
                    }
                }
            }
        }
    }
`;var g=t(79570),m=(0,l.pM)({components:{DataTableStyleWrap:p,TableWrapper:g.AC},props:{filterOption:d.Ay.bool,filterOnchange:d.Ay.bool,rowSelection:d.Ay.bool,defaultSelected:d.Ay.array,tableData:d.Ay.array,columns:d.Ay.array,handleDataSearch:d.Ay.func,handleAdd:d.Ay.func,handleBack:d.Ay.func,handleImport:d.Ay.func,handleExport:d.Ay.func,rowClassFunc:{type:Function,default:()=>{}},backOption:{type:Boolean,default:!1},addOption:{type:Boolean,default:!1},exportOption:{type:Boolean,default:!1},importOption:{type:Boolean,default:!1},expandedRow:{type:Object,default:null},childrenColumnName:{type:String,default:"children"},backTitle:{type:String,default:""}},setup(e,{emit:a}){const t=(0,u.KR)([]);(0,l.wB)((()=>e.defaultSelected),(e=>{t.value=e}),{immediate:!0});const n=e=>{t.value=e,a("onSelectChange",t.value)},o=(0,l.EW)((()=>({selectedRowKeys:(0,u.R1)(t),onChange:n,hideDefaultSelections:!0}))),i=(0,u.KR)(10),r=(0,u.KR)(["10","20","50","100"]),s=e=>{i.value=e.pageSize},d=({record:a})=>e.expandedRow.innerDataProp?a[e.expandedRow.innerDataProp]:a.children,c=(a,t)=>e.rowClassFunc(a)??t%2===1?"table-striped row-style":"row-style";return{pageSize:i,pageSizeOptions:r,rowSelections:o,changePageSize:s,getInnerData:d,getRowClassName:c}}}),h=t(66262);const b=(0,h.A)(m,[["render",s],["__scopeId","data-v-1b881e36"]]);var f=b},75126:function(e,a,t){t(16859)},82077:function(e,a,t){t.d(a,{M:function(){return n}});var l=t(92317);function n(e,a){const t=(0,l.Gq)("tagList");if(!t)return null;const n=t[e];return n?n[a]:null}},82958:function(e,a,t){t.d(a,{J:function(){return i}});var l=t(79841),n=t(92317),o=t(75220);function i(e){const{name:a}=(0,o.lq)();let t="";t=e||a;const i=(0,n.Gq)("permission"),r=(0,l.Kh)({read:i[t]?.includes("r"),create:i[t]?.includes("c"),update:i[t]?.includes("u"),delete:i[t]?.includes("d")});return{permission:r}}},94734:function(e,a,t){t.d(a,{A:function(){return p}});var l=t(20641),n=t(9322),o=t(72644);function i(e,a,t,i,r,s){const u=(0,l.g2)("a-select-option"),d=(0,l.g2)("a-select"),c=(0,l.g2)("a-space");return(0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(c,null,{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.levels,(t=>((0,l.uX)(),(0,l.Wv)(d,{value:e.selected[t],"onUpdate:value":a=>e.selected[t]=a,key:t,style:{width:"100%","min-width":"100px"},onChange:a=>e.handleChange(t)},{default:(0,l.k6)((()=>[e.nullOption?((0,l.uX)(),(0,l.Wv)(u,{key:0,value:null,onClick:a[0]||(a[0]=(0,n.D$)((()=>{}),["stop"]))},{default:(0,l.k6)((()=>[(0,l.eW)("無")])),_:1})):(0,l.Q3)("",!0),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.getNextLevel(t),((t,i)=>((0,l.uX)(),(0,l.Wv)(u,{value:JSON.stringify(t),key:i,onClick:a[1]||(a[1]=(0,n.D$)((()=>{}),["stop"]))},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(t[e.childName]),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","onChange"])))),128))])),_:1})])}t(44114);var r=t(79841),s=t(19732),u=(0,l.pM)({name:"LevelSelect",props:{selectedValue:{type:Object,default:null},group:s.Ay.array,nullOption:{type:Boolean,default:!1},childName:{type:String,default:"name"},childProp:{type:String,default:"children"}},setup(e,{emit:a}){const t=(0,r.KR)(e.selectedValue||{}),n=(0,r.KR)(e.selectedValue?Array.from(Array(Object.keys(e.selectedValue).length).keys()):[0]),o=(0,l.EW)((()=>e.group?e.group:[])),i=a=>{if(0===a)return o.value;const l=n.value[a-1],i=JSON.parse(t.value[l]);return i&&i[e.childProp]&&i[e.childProp].length>0?i[e.childProp]:[]},s=l=>{const o=n.value.indexOf(l);for(let e=o+1;e<n.value.length;e++)delete t.value[e];if(n.value.splice(o+1,n.value.length-(o+1)),t.value[l]){a("change",JSON.parse(t.value[n.value.length-1]),t.value);const o=JSON.parse(t.value[l]);o[e.childProp]&&n.value.push(l+1)}else{const e=n.value.length>1?t.value[n.value.length-2]:null;a("change",JSON.parse(e),t.value)}};return{selected:t,levels:n,getNextLevel:i,handleChange:s}}}),d=t(66262);const c=(0,d.A)(u,[["render",i]]);var p=c}}]);